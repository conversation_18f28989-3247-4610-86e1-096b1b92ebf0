#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证CoreMail AI修复的完整脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from src.core.config_manager import ConfigManager
        from src.core.email_client import create_email_client, CoreMailClient
        from src.ui.floating_ball import FloatingBall
        from src.ui.main_window import MainWindow
        from src.ui.config_dialog import ConfigDialog
        from src.utils.logger import setup_logger
        
        print("✅ 所有模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n🔧 测试配置加载...")
    
    try:
        from src.core.config_manager import ConfigManager
        from src.utils.logger import setup_logger
        
        # 设置日志
        logger = setup_logger()
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 获取邮件配置
        email_config = config_manager.get_email_config()
        
        print(f"📧 邮件配置:")
        print(f"   服务器: {email_config.get('server')}")
        print(f"   协议: {email_config.get('protocol')}")
        print(f"   端口: {email_config.get('port')}")
        
        # 检查是否正确加载了Coremail配置
        if email_config.get('protocol', '').upper() == 'COREMAIL':
            print("✅ Coremail协议配置正确加载")
            return True, email_config
        else:
            print(f"⚠️  协议配置: {email_config.get('protocol')} (期望: Coremail)")
            return False, email_config
            
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False, None


def test_coremail_client():
    """测试CoreMail客户端"""
    print("\n📧 测试CoreMail客户端...")
    
    try:
        from src.core.email_client import create_email_client, CoreMailClient
        
        # 测试配置
        test_config = {
            "server": "https://mails.dfmc.com.cn/coremail",
            "protocol": "Coremail",
            "port": 993,
            "ssl": True,
            "username": "<EMAIL>",
            "password": "test123"
        }
        
        print(f"原始服务器配置: {test_config['server']}")
        
        # 创建客户端
        client = create_email_client(test_config)
        
        print(f"✅ 客户端创建成功: {type(client).__name__}")
        
        if isinstance(client, CoreMailClient):
            processed_server = client.config.get('server')
            print(f"处理后服务器: {processed_server}")
            print(f"端口: {client.config.get('port')}")
            print(f"SSL: {client.config.get('ssl')}")
            return True
        else:
            print(f"❌ 期望CoreMailClient，实际: {type(client).__name__}")
            return False
            
    except Exception as e:
        print(f"❌ CoreMail客户端测试失败: {e}")
        return False


def test_gui_components():
    """测试GUI组件（不显示窗口）"""
    print("\n🖥️  测试GUI组件...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.core.config_manager import ConfigManager
        from src.ui.floating_ball import FloatingBall
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建悬浮球（不显示）
        floating_ball = FloatingBall(config_manager)
        
        print("✅ 悬浮球组件创建成功")
        
        # 测试悬浮球的方法是否存在
        methods_to_check = [
            '_show_context_menu',
            '_show_main_window', 
            '_show_config_dialog',
            'mousePressEvent',
            'mouseMoveEvent',
            'mouseReleaseEvent'
        ]
        
        for method_name in methods_to_check:
            if hasattr(floating_ball, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件测试失败: {e}")
        return False


def test_default_config_file():
    """测试默认配置文件"""
    print("\n📄 测试默认配置文件...")
    
    try:
        config_file = Path("resources/config/default_config.yaml")
        
        if not config_file.exists():
            print(f"❌ 默认配置文件不存在: {config_file}")
            return False
        
        print(f"✅ 默认配置文件存在: {config_file}")
        
        # 读取配置文件内容
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        email_config = config.get('email', {})
        server = email_config.get('server', '')
        protocol = email_config.get('protocol', '')
        
        print(f"配置文件中的服务器: {server}")
        print(f"配置文件中的协议: {protocol}")
        
        if 'mails.dfmc.com.cn' in server and protocol.upper() == 'COREMAIL':
            print("✅ 配置文件内容正确")
            return True
        else:
            print("⚠️  配置文件内容可能需要检查")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🎯 CoreMail AI 修复验证")
    print("=" * 60)
    
    results = []
    
    # 测试1: 模块导入
    results.append(("模块导入", test_imports()))
    
    # 测试2: 配置加载
    config_result, email_config = test_config_loading()
    results.append(("配置加载", config_result))
    
    # 测试3: CoreMail客户端
    results.append(("CoreMail客户端", test_coremail_client()))
    
    # 测试4: GUI组件
    results.append(("GUI组件", test_gui_components()))
    
    # 测试5: 默认配置文件
    results.append(("默认配置文件", test_default_config_file()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！修复成功！")
        print("\n💡 现在您可以:")
        print("1. 运行 python src/main.py 启动应用")
        print("2. 右键点击悬浮球查看菜单")
        print("3. 拖拽悬浮球移动位置")
        print("4. 在设置中配置您的邮箱信息")
    else:
        print(f"\n⚠️  {total - passed} 项测试失败，请检查相关问题")
    
    print("\n📝 修复内容:")
    print("1. ✅ 修复了悬浮球拖拽功能")
    print("2. ✅ 修复了右键菜单显示")
    print("3. ✅ 添加了Coremail协议支持")
    print("4. ✅ 改进了配置加载逻辑")
    print("5. ✅ 增加了详细的日志输出")


if __name__ == "__main__":
    main()
