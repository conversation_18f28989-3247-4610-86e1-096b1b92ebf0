#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助函数模块
"""

import os
import re
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any
from email.utils import parseaddr
from cryptography.fernet import Fe<PERSON><PERSON>


def validate_email(email: str) -> bool:
    """
    验证邮箱地址格式
    
    Args:
        email (str): 邮箱地址
    
    Returns:
        bool: 是否有效
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def parse_email_address(email_str: str) -> tuple:
    """
    解析邮箱地址字符串
    
    Args:
        email_str (str): 邮箱地址字符串，如 "张三 <<EMAIL>>"
    
    Returns:
        tuple: (姓名, 邮箱地址)
    """
    name, addr = parseaddr(email_str)
    return name.strip(), addr.strip()


def generate_key() -> bytes:
    """
    生成加密密钥
    
    Returns:
        bytes: 加密密钥
    """
    return Fernet.generate_key()


def encrypt_password(password: str, key: bytes) -> str:
    """
    加密密码
    
    Args:
        password (str): 明文密码
        key (bytes): 加密密钥
    
    Returns:
        str: 加密后的密码
    """
    f = Fernet(key)
    encrypted = f.encrypt(password.encode())
    return encrypted.decode()


def decrypt_password(encrypted_password: str, key: bytes) -> str:
    """
    解密密码
    
    Args:
        encrypted_password (str): 加密的密码
        key (bytes): 解密密钥
    
    Returns:
        str: 明文密码
    """
    f = Fernet(key)
    decrypted = f.decrypt(encrypted_password.encode())
    return decrypted.decode()


def get_file_hash(file_path: Path) -> str:
    """
    获取文件的MD5哈希值
    
    Args:
        file_path (Path): 文件路径
    
    Returns:
        str: MD5哈希值
    """
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def ensure_dir(dir_path: Path) -> None:
    """
    确保目录存在
    
    Args:
        dir_path (Path): 目录路径
    """
    dir_path.mkdir(parents=True, exist_ok=True)


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    截断文本
    
    Args:
        text (str): 原始文本
        max_length (int): 最大长度
        suffix (str): 后缀
    
    Returns:
        str: 截断后的文本
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes (int): 字节数
    
    Returns:
        str: 格式化后的大小
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def safe_get(dictionary: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    安全获取字典值
    
    Args:
        dictionary (Dict): 字典
        key (str): 键
        default (Any): 默认值
    
    Returns:
        Any: 值或默认值
    """
    return dictionary.get(key, default)
