#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试
"""

import sys
import os
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager
from src.core.email_client import EmailMessage
from src.core.ai_analyzer import EmailAnalyzer, AnalysisType
from src.utils.helpers import validate_email, parse_email_address


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
    
    def test_get_default_config(self):
        """测试获取默认配置"""
        app_name = self.config_manager.get("app.name")
        self.assertEqual(app_name, "CoreMail AI")
        
        email_port = self.config_manager.get("email.port")
        self.assertEqual(email_port, 993)
    
    def test_set_and_get_config(self):
        """测试设置和获取配置"""
        test_value = "test_server.com"
        self.config_manager.set("email.server", test_value)
        
        retrieved_value = self.config_manager.get("email.server")
        self.assertEqual(retrieved_value, test_value)
    
    def test_nested_config(self):
        """测试嵌套配置"""
        self.config_manager.set("test.nested.value", "nested_test")
        
        retrieved_value = self.config_manager.get("test.nested.value")
        self.assertEqual(retrieved_value, "nested_test")


class TestHelpers(unittest.TestCase):
    """辅助函数测试"""
    
    def test_validate_email(self):
        """测试邮箱验证"""
        # 有效邮箱
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        
        # 无效邮箱
        self.assertFalse(validate_email("invalid-email"))
        self.assertFalse(validate_email("@domain.com"))
        self.assertFalse(validate_email("test@"))
        self.assertFalse(validate_email(""))
    
    def test_parse_email_address(self):
        """测试邮箱地址解析"""
        # 带姓名的邮箱
        name, addr = parse_email_address("张三 <<EMAIL>>")
        self.assertEqual(name, "张三")
        self.assertEqual(addr, "<EMAIL>")
        
        # 纯邮箱地址
        name, addr = parse_email_address("<EMAIL>")
        self.assertEqual(name, "")
        self.assertEqual(addr, "<EMAIL>")
        
        # 空字符串
        name, addr = parse_email_address("")
        self.assertEqual(name, "")
        self.assertEqual(addr, "")


class TestEmailMessage(unittest.TestCase):
    """邮件消息测试"""
    
    def test_email_message_creation(self):
        """测试邮件消息创建"""
        msg = EmailMessage()
        msg.uid = "123"
        msg.subject = "测试邮件"
        msg.sender = "<EMAIL>"
        msg.content_text = "这是一封测试邮件"
        
        self.assertEqual(msg.uid, "123")
        self.assertEqual(msg.subject, "测试邮件")
        self.assertEqual(msg.sender, "<EMAIL>")
        self.assertEqual(msg.content_text, "这是一封测试邮件")
    
    def test_email_message_to_dict(self):
        """测试邮件消息转字典"""
        msg = EmailMessage()
        msg.uid = "123"
        msg.subject = "测试邮件"
        msg.sender = "<EMAIL>"
        
        msg_dict = msg.to_dict()
        
        self.assertIsInstance(msg_dict, dict)
        self.assertEqual(msg_dict["uid"], "123")
        self.assertEqual(msg_dict["subject"], "测试邮件")
        self.assertEqual(msg_dict["sender"], "<EMAIL>")


class TestEmailAnalyzer(unittest.TestCase):
    """邮件分析器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
        # 设置测试用的硅基流动配置（使用空的API密钥进行测试）
        sf_config = {
            "api_key": "",  # 测试时使用空密钥
            "base_url": "https://api.siliconflow.cn/v1",
            "model": "Qwen/Qwen2.5-72B-Instruct",
            "max_tokens": 2000,
            "temperature": 0.7
        }
        self.config_manager.set_siliconflow_config(sf_config)
        
        self.analyzer = EmailAnalyzer(self.config_manager)
    
    def test_get_prompt_template(self):
        """测试获取提示词模板"""
        summary_prompt = self.analyzer.get_prompt_template(AnalysisType.SUMMARY)
        self.assertIsInstance(summary_prompt, str)
        self.assertIn("{email_content}", summary_prompt)
        
        todo_prompt = self.analyzer.get_prompt_template(AnalysisType.TODO)
        self.assertIsInstance(todo_prompt, str)
        self.assertIn("{email_content}", todo_prompt)
        
        reply_prompt = self.analyzer.get_prompt_template(AnalysisType.REPLY)
        self.assertIsInstance(reply_prompt, str)
        self.assertIn("{email_content}", reply_prompt)
    
    def test_prepare_email_content(self):
        """测试准备邮件内容"""
        msg = EmailMessage()
        msg.subject = "测试邮件"
        msg.sender = "<EMAIL>"
        msg.content_text = "这是邮件正文内容"
        
        content = self.analyzer._prepare_email_content(msg)
        
        self.assertIsInstance(content, str)
        self.assertIn("测试邮件", content)
        self.assertIn("<EMAIL>", content)
        self.assertIn("这是邮件正文内容", content)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_config_email_integration(self):
        """测试配置和邮件集成"""
        config_manager = ConfigManager()
        
        # 设置邮件配置
        email_config = {
            "server": "test.example.com",
            "port": 993,
            "protocol": "IMAP",
            "ssl": True,
            "username": "<EMAIL>",
            "password": "test_password"
        }
        config_manager.set_email_config(email_config)
        
        # 获取配置
        retrieved_config = config_manager.get_email_config()
        
        self.assertEqual(retrieved_config["server"], "test.example.com")
        self.assertEqual(retrieved_config["port"], 993)
        self.assertEqual(retrieved_config["username"], "<EMAIL>")
        # 密码应该被解密
        self.assertEqual(retrieved_config["password"], "test_password")


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestConfigManager,
        TestHelpers,
        TestEmailMessage,
        TestEmailAnalyzer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("开始运行 CoreMail AI 基础功能测试...")
    print("=" * 50)
    
    success = run_tests()
    
    print("=" * 50)
    if success:
        print("✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败！")
        sys.exit(1)
