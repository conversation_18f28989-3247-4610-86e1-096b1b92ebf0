#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoreMail AI 邮件智能分析应用主程序
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger
from src.core.config_manager import ConfigManager
from src.ui.floating_ball import FloatingBall


class CoreMailAIApp:
    """CoreMail AI 应用主类"""
    
    def __init__(self):
        self.app = None
        self.floating_ball = None
        self.config_manager = None
        self.logger = None
        
    def initialize(self):
        """初始化应用"""
        # 设置日志
        self.logger = setup_logger()
        self.logger.info("正在启动 CoreMail AI 应用...")
        
        # 创建QApplication
        self.app = QApplication(sys.argv)
        self.app.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出应用
        
        # 设置应用属性
        self.app.setApplicationName("CoreMail AI")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("CoreMail AI Team")
        
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 创建悬浮球
        self.floating_ball = FloatingBall(self.config_manager)
        
        self.logger.info("CoreMail AI 应用初始化完成")
        
    def run(self):
        """运行应用"""
        try:
            self.initialize()
            
            # 显示悬浮球
            self.floating_ball.show()
            
            self.logger.info("CoreMail AI 应用已启动")
            
            # 运行应用主循环
            return self.app.exec()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"应用运行出错: {e}")
            else:
                print(f"应用运行出错: {e}")
            return 1
        
    def cleanup(self):
        """清理资源"""
        if self.logger:
            self.logger.info("正在关闭 CoreMail AI 应用...")
        
        if self.floating_ball:
            self.floating_ball.close()
            
        if self.app:
            self.app.quit()


def main():
    """主函数"""
    app = CoreMailAIApp()
    
    try:
        exit_code = app.run()
    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
        exit_code = 0
    finally:
        app.cleanup()
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
