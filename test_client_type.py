#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细验证邮件客户端类型的测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager
from src.core.email_client import create_email_client, IMAPClient, CoreMailClient
from src.utils.logger import setup_logger


def test_config_protocol():
    """测试配置中的协议设置"""
    print("=" * 60)
    print("🔧 测试配置协议设置")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger()
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 获取原始配置
        raw_config = config_manager.config
        email_raw = raw_config.get("email", {})
        
        print(f"📋 原始配置中的协议: {email_raw.get('protocol')}")
        print(f"📋 原始配置中的服务器: {email_raw.get('server')}")
        
        # 获取处理后的邮件配置
        email_config = config_manager.get_email_config()
        
        print(f"📧 处理后配置中的协议: {email_config.get('protocol')}")
        print(f"📧 处理后配置中的服务器: {email_config.get('server')}")
        
        return email_config
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_client_creation_detailed(config):
    """详细测试客户端创建过程"""
    print("\n" + "=" * 60)
    print("🏭 详细测试客户端创建过程")
    print("=" * 60)
    
    if not config:
        print("❌ 无配置信息，跳过测试")
        return None
    
    try:
        print(f"📥 输入配置:")
        for key, value in config.items():
            if key == 'password':
                print(f"   {key}: {'***已设置***' if value else '未设置'}")
            else:
                print(f"   {key}: {value}")
        
        print(f"\n🔍 协议判断:")
        protocol = config.get("protocol", "IMAP").upper()
        print(f"   原始协议: {config.get('protocol')}")
        print(f"   转换后协议: {protocol}")
        
        # 手动判断应该创建什么类型的客户端
        if protocol == "COREMAIL":
            expected_type = "CoreMailClient"
        elif protocol == "IMAP":
            expected_type = "IMAPClient"
        else:
            expected_type = "Unknown"
        
        print(f"   期望客户端类型: {expected_type}")
        
        # 实际创建客户端
        print(f"\n🏗️  创建客户端...")
        client = create_email_client(config)
        
        actual_type = type(client).__name__
        print(f"   实际客户端类型: {actual_type}")
        
        # 验证类型是否匹配
        if actual_type == expected_type:
            print(f"✅ 客户端类型匹配！")
        else:
            print(f"❌ 客户端类型不匹配！期望: {expected_type}, 实际: {actual_type}")
        
        # 检查继承关系
        print(f"\n🔗 继承关系检查:")
        print(f"   是否为EmailClient: {hasattr(client, 'connect')}")
        print(f"   是否为IMAPClient: {isinstance(client, IMAPClient)}")
        print(f"   是否为CoreMailClient: {isinstance(client, CoreMailClient)}")
        
        # 检查配置处理
        if isinstance(client, CoreMailClient):
            print(f"\n⚙️  CoreMail配置处理:")
            print(f"   原始服务器: {config.get('server')}")
            print(f"   处理后服务器: {client.config.get('server')}")
            print(f"   端口: {client.config.get('port')}")
            print(f"   SSL: {client.config.get('ssl')}")
        
        return client
        
    except Exception as e:
        print(f"❌ 客户端创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_different_protocols():
    """测试不同协议的客户端创建"""
    print("\n" + "=" * 60)
    print("🧪 测试不同协议的客户端创建")
    print("=" * 60)
    
    test_configs = [
        {
            "name": "Coremail协议",
            "config": {
                "server": "https://mails.dfmc.com.cn/coremail",
                "protocol": "Coremail",
                "port": 993,
                "ssl": True,
                "username": "<EMAIL>",
                "password": "test123"
            },
            "expected": "CoreMailClient"
        },
        {
            "name": "IMAP协议",
            "config": {
                "server": "imap.gmail.com",
                "protocol": "IMAP",
                "port": 993,
                "ssl": True,
                "username": "<EMAIL>",
                "password": "test123"
            },
            "expected": "IMAPClient"
        },
        {
            "name": "大写COREMAIL协议",
            "config": {
                "server": "https://mail.example.com/coremail",
                "protocol": "COREMAIL",
                "port": 993,
                "ssl": True,
                "username": "<EMAIL>",
                "password": "test123"
            },
            "expected": "CoreMailClient"
        }
    ]
    
    results = []
    
    for test_case in test_configs:
        print(f"\n📋 测试: {test_case['name']}")
        print(f"   协议: {test_case['config']['protocol']}")
        print(f"   期望类型: {test_case['expected']}")
        
        try:
            client = create_email_client(test_case['config'])
            actual_type = type(client).__name__
            
            print(f"   实际类型: {actual_type}")
            
            if actual_type == test_case['expected']:
                print(f"   ✅ 测试通过")
                results.append(True)
            else:
                print(f"   ❌ 测试失败")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ 创建失败: {e}")
            results.append(False)
    
    return results


def test_actual_usage():
    """测试实际使用场景"""
    print("\n" + "=" * 60)
    print("🎯 测试实际使用场景")
    print("=" * 60)
    
    try:
        # 模拟主窗口中的邮件客户端创建过程
        from src.ui.main_window import EmailWorker
        from src.core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        worker = EmailWorker(config_manager)
        
        # 获取邮件配置
        email_config = config_manager.get_email_config()
        print(f"📧 主窗口获取的配置:")
        print(f"   协议: {email_config.get('protocol')}")
        print(f"   服务器: {email_config.get('server')}")
        
        # 创建邮件客户端（模拟工作线程中的过程）
        from src.core.email_client import create_email_client
        client = create_email_client(email_config)
        
        print(f"🏭 主窗口创建的客户端类型: {type(client).__name__}")
        
        if isinstance(client, CoreMailClient):
            print(f"✅ 主窗口正确使用了CoreMailClient")
            return True
        else:
            print(f"❌ 主窗口使用了错误的客户端类型")
            return False
            
    except Exception as e:
        print(f"❌ 实际使用场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎯 邮件客户端类型详细验证")
    print("验证 CoreMailClient 是否被正确使用")
    
    # 测试1: 配置协议设置
    config = test_config_protocol()
    
    # 测试2: 详细客户端创建过程
    client = test_client_creation_detailed(config)
    
    # 测试3: 不同协议测试
    protocol_results = test_different_protocols()
    
    # 测试4: 实际使用场景
    usage_result = test_actual_usage()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    print(f"配置加载: {'✅' if config else '❌'}")
    print(f"客户端创建: {'✅' if client else '❌'}")
    print(f"协议测试: {'✅' if all(protocol_results) else '❌'} ({sum(protocol_results)}/{len(protocol_results)})")
    print(f"实际使用: {'✅' if usage_result else '❌'}")
    
    if config and isinstance(client, CoreMailClient) and all(protocol_results) and usage_result:
        print("\n🎉 所有测试通过！CoreMailClient 正确工作！")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    # 最终验证
    if config and config.get('protocol', '').upper() == 'COREMAIL':
        if isinstance(client, CoreMailClient):
            print(f"\n✅ 最终确认: Coremail协议正确使用了CoreMailClient")
        else:
            print(f"\n❌ 最终确认: Coremail协议使用了错误的客户端类型: {type(client).__name__}")


if __name__ == "__main__":
    main()
