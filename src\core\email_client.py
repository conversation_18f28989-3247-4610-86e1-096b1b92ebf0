#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件客户端模块
"""

import imaplib
import poplib
import email
import ssl
from typing import List, Dict, Any, Optional, Tuple
from email.header import decode_header
from email.utils import parsedate_to_datetime
from datetime import datetime

from src.utils.logger import get_logger
from src.utils.helpers import parse_email_address, validate_email


class EmailMessage:
    """邮件消息类"""
    
    def __init__(self):
        self.uid = ""
        self.subject = ""
        self.sender = ""
        self.sender_name = ""
        self.sender_email = ""
        self.recipients = []
        self.cc = []
        self.bcc = []
        self.date = None
        self.content_text = ""
        self.content_html = ""
        self.attachments = []
        self.size = 0
        self.flags = []
        self.raw_message = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "uid": self.uid,
            "subject": self.subject,
            "sender": self.sender,
            "sender_name": self.sender_name,
            "sender_email": self.sender_email,
            "recipients": self.recipients,
            "cc": self.cc,
            "bcc": self.bcc,
            "date": self.date.isoformat() if self.date else None,
            "content_text": self.content_text,
            "content_html": self.content_html,
            "attachments": self.attachments,
            "size": self.size,
            "flags": self.flags
        }


class EmailClient:
    """邮件客户端基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger("EmailClient")
        self.connection = None
        self.is_connected = False
    
    def connect(self) -> bool:
        """连接到邮件服务器"""
        raise NotImplementedError
    
    def disconnect(self):
        """断开连接"""
        raise NotImplementedError
    
    def get_folders(self) -> List[str]:
        """获取邮件文件夹列表"""
        raise NotImplementedError
    
    def get_messages(self, folder: str = "INBOX", limit: int = 50) -> List[EmailMessage]:
        """获取邮件列表"""
        raise NotImplementedError
    
    def get_message_by_uid(self, uid: str, folder: str = "INBOX") -> Optional[EmailMessage]:
        """根据UID获取邮件"""
        raise NotImplementedError


class IMAPClient(EmailClient):
    """IMAP邮件客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = get_logger("IMAPClient")
    
    def connect(self) -> bool:
        """连接到IMAP服务器"""
        try:
            server = self.config.get("server")
            port = self.config.get("port", 993)
            username = self.config.get("username")
            password = self.config.get("password")
            use_ssl = self.config.get("ssl", True)
            timeout = self.config.get("timeout", 30)
            
            if not all([server, username, password]):
                raise ValueError("服务器地址、用户名和密码不能为空")
            
            if not validate_email(username):
                raise ValueError("用户名必须是有效的邮箱地址")
            
            self.logger.info(f"正在连接到IMAP服务器: {server}:{port}")
            
            # 创建IMAP连接
            if use_ssl:
                self.connection = imaplib.IMAP4_SSL(server, port, timeout=timeout)
            else:
                self.connection = imaplib.IMAP4(server, port)
            
            # 登录
            self.connection.login(username, password)
            
            self.is_connected = True
            self.logger.info("IMAP连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"IMAP连接失败: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开IMAP连接"""
        try:
            if self.connection and self.is_connected:
                self.connection.close()
                self.connection.logout()
                self.is_connected = False
                self.logger.info("IMAP连接已断开")
        except Exception as e:
            self.logger.error(f"断开IMAP连接时出错: {e}")
    
    def get_folders(self) -> List[str]:
        """获取IMAP文件夹列表"""
        try:
            if not self.is_connected:
                raise ConnectionError("未连接到服务器")
            
            status, folders = self.connection.list()
            if status != 'OK':
                raise Exception("获取文件夹列表失败")
            
            folder_list = []
            for folder in folders:
                # 解析文件夹名称
                folder_str = folder.decode('utf-8')
                # 提取文件夹名称（简化处理）
                parts = folder_str.split('"')
                if len(parts) >= 3:
                    folder_name = parts[-2]
                    folder_list.append(folder_name)
            
            return folder_list
            
        except Exception as e:
            self.logger.error(f"获取文件夹列表失败: {e}")
            return []
    
    def get_messages(self, folder: str = "INBOX", limit: int = 50) -> List[EmailMessage]:
        """获取IMAP邮件列表"""
        try:
            if not self.is_connected:
                raise ConnectionError("未连接到服务器")
            
            # 选择文件夹
            status, count = self.connection.select(folder)
            if status != 'OK':
                raise Exception(f"选择文件夹 {folder} 失败")
            
            # 搜索邮件
            status, messages = self.connection.search(None, 'ALL')
            if status != 'OK':
                raise Exception("搜索邮件失败")
            
            message_ids = messages[0].split()
            
            # 限制邮件数量，获取最新的邮件
            if limit > 0:
                message_ids = message_ids[-limit:]
            
            email_messages = []
            
            for msg_id in message_ids:
                try:
                    email_msg = self._fetch_message(msg_id, folder)
                    if email_msg:
                        email_messages.append(email_msg)
                except Exception as e:
                    self.logger.warning(f"获取邮件 {msg_id} 失败: {e}")
                    continue
            
            # 按日期倒序排列
            email_messages.sort(key=lambda x: x.date or datetime.min, reverse=True)
            
            self.logger.info(f"成功获取 {len(email_messages)} 封邮件")
            return email_messages
            
        except Exception as e:
            self.logger.error(f"获取邮件列表失败: {e}")
            return []
    
    def get_message_by_uid(self, uid: str, folder: str = "INBOX") -> Optional[EmailMessage]:
        """根据UID获取IMAP邮件"""
        try:
            if not self.is_connected:
                raise ConnectionError("未连接到服务器")
            
            # 选择文件夹
            status, count = self.connection.select(folder)
            if status != 'OK':
                raise Exception(f"选择文件夹 {folder} 失败")
            
            return self._fetch_message(uid.encode(), folder)
            
        except Exception as e:
            self.logger.error(f"根据UID获取邮件失败: {e}")
            return None
    
    def _fetch_message(self, msg_id: bytes, folder: str) -> Optional[EmailMessage]:
        """获取单个邮件"""
        try:
            # 获取邮件数据
            status, msg_data = self.connection.fetch(msg_id, '(RFC822)')
            if status != 'OK':
                return None
            
            # 解析邮件
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # 创建EmailMessage对象
            msg = EmailMessage()
            msg.uid = msg_id.decode()
            msg.raw_message = email_message
            
            # 解析邮件头
            msg.subject = self._decode_header(email_message.get('Subject', ''))
            
            # 解析发件人
            sender = email_message.get('From', '')
            msg.sender = sender
            msg.sender_name, msg.sender_email = parse_email_address(sender)
            
            # 解析收件人
            msg.recipients = self._parse_addresses(email_message.get('To', ''))
            msg.cc = self._parse_addresses(email_message.get('Cc', ''))
            msg.bcc = self._parse_addresses(email_message.get('Bcc', ''))
            
            # 解析日期
            date_str = email_message.get('Date')
            if date_str:
                try:
                    msg.date = parsedate_to_datetime(date_str)
                except:
                    pass
            
            # 解析邮件内容
            msg.content_text, msg.content_html, msg.attachments = self._parse_content(email_message)
            
            # 获取邮件大小
            msg.size = len(raw_email)
            
            return msg
            
        except Exception as e:
            self.logger.error(f"解析邮件失败: {e}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """解码邮件头"""
        try:
            decoded_parts = decode_header(header)
            decoded_str = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_str += part.decode(encoding)
                    else:
                        decoded_str += part.decode('utf-8', errors='ignore')
                else:
                    decoded_str += part
            
            return decoded_str.strip()
            
        except Exception as e:
            self.logger.warning(f"解码邮件头失败: {e}")
            return header
    
    def _parse_addresses(self, addr_str: str) -> List[str]:
        """解析邮件地址列表"""
        if not addr_str:
            return []
        
        addresses = []
        try:
            # 简单的地址解析
            for addr in addr_str.split(','):
                addr = addr.strip()
                if addr:
                    addresses.append(addr)
        except Exception as e:
            self.logger.warning(f"解析邮件地址失败: {e}")
        
        return addresses
    
    def _parse_content(self, email_message) -> Tuple[str, str, List[Dict]]:
        """解析邮件内容"""
        text_content = ""
        html_content = ""
        attachments = []
        
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition', ''))
                    
                    if 'attachment' in content_disposition:
                        # 处理附件
                        filename = part.get_filename()
                        if filename:
                            attachments.append({
                                'filename': self._decode_header(filename),
                                'content_type': content_type,
                                'size': len(part.get_payload(decode=True) or b'')
                            })
                    elif content_type == 'text/plain':
                        # 纯文本内容
                        payload = part.get_payload(decode=True)
                        if payload:
                            charset = part.get_content_charset() or 'utf-8'
                            text_content += payload.decode(charset, errors='ignore')
                    elif content_type == 'text/html':
                        # HTML内容
                        payload = part.get_payload(decode=True)
                        if payload:
                            charset = part.get_content_charset() or 'utf-8'
                            html_content += payload.decode(charset, errors='ignore')
            else:
                # 非多部分邮件
                content_type = email_message.get_content_type()
                payload = email_message.get_payload(decode=True)
                
                if payload:
                    charset = email_message.get_content_charset() or 'utf-8'
                    content = payload.decode(charset, errors='ignore')
                    
                    if content_type == 'text/html':
                        html_content = content
                    else:
                        text_content = content
        
        except Exception as e:
            self.logger.error(f"解析邮件内容失败: {e}")
        
        return text_content, html_content, attachments


def create_email_client(config: Dict[str, Any]) -> EmailClient:
    """
    创建邮件客户端
    
    Args:
        config (Dict): 邮件配置
    
    Returns:
        EmailClient: 邮件客户端实例
    """
    protocol = config.get("protocol", "IMAP").upper()
    
    if protocol == "IMAP":
        return IMAPClient(config)
    elif protocol == "POP3":
        # TODO: 实现POP3客户端
        raise NotImplementedError("POP3客户端尚未实现")
    else:
        raise ValueError(f"不支持的协议: {protocol}")
