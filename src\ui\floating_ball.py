#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
悬浮球界面模块
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QSystemTrayIcon, QMenu, QApplication)
from PyQt6.QtCore import Qt, QPoint, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QPainter, QBrush, QColor, QPen, QFont, QPixmap

from src.utils.logger import get_logger
from src.ui.config_dialog import ConfigDialog
from src.ui.main_window import MainWindow


class FloatingBall(QWidget):
    """悬浮球窗口"""
    
    # 信号
    show_main_window = pyqtSignal()
    show_config_dialog = pyqtSignal()
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger("FloatingBall")

        # 窗口属性
        self.dragging = False
        self.drag_position = QPoint()

        # 子窗口
        self.main_window = None
        self.config_dialog = None

        # 初始化UI
        self._init_ui()

        # 创建系统托盘
        self._create_system_tray()

        self.logger.info("悬浮球界面初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 获取UI配置
        ui_config = self.config_manager.get_ui_config()
        ball_config = ui_config.get("floating_ball", {})
        
        # 窗口设置
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 窗口大小和位置
        size = ball_config.get("size", 60)
        self.setFixedSize(size, size)
        
        position = ball_config.get("position", {"x": 100, "y": 100})
        self.move(position["x"], position["y"])
        
        # 设置透明度
        opacity = ball_config.get("opacity", 0.8)
        self.setWindowOpacity(opacity)
        
        # 创建布局
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)
        
        # 创建主按钮
        self.main_button = QPushButton()
        self.main_button.setFixedSize(size, size)
        self.main_button.clicked.connect(self._on_main_button_clicked)
        
        # 设置按钮样式
        self._set_button_style()
        
        layout.addWidget(self.main_button)
        
        # 工具提示
        self.setToolTip("CoreMail AI - 点击展开主界面")
    
    def _set_button_style(self):
        """设置按钮样式"""
        style = """
        QPushButton {
            background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #4A90E2, stop:1 #357ABD);
            border: 2px solid #2E5C8A;
            border-radius: 30px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #5BA0F2, stop:1 #4A8ACD);
            border: 2px solid #3E6C9A;
        }
        QPushButton:pressed {
            background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #3A80D2, stop:1 #2E6AAD);
        }
        """
        self.main_button.setStyleSheet(style)
        self.main_button.setText("📧\nAI")
    
    def _create_system_tray(self):
        """创建系统托盘"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            self.logger.warning("系统托盘不可用")
            return
        
        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon(self)
        
        # 设置图标（这里使用简单的文本图标，实际项目中应该使用图片）
        pixmap = QPixmap(16, 16)
        pixmap.fill(QColor(74, 144, 226))
        self.tray_icon.setIcon(QIcon(pixmap))
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        show_action = tray_menu.addAction("显示悬浮球")
        show_action.triggered.connect(self.show)
        
        hide_action = tray_menu.addAction("隐藏悬浮球")
        hide_action.triggered.connect(self.hide)
        
        tray_menu.addSeparator()
        
        config_action = tray_menu.addAction("设置")
        config_action.triggered.connect(self._show_config_dialog)
        
        tray_menu.addSeparator()
        
        quit_action = tray_menu.addAction("退出")
        quit_action.triggered.connect(self._quit_application)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()
        
        # 托盘图标双击事件
        self.tray_icon.activated.connect(self._on_tray_activated)
    
    def _on_tray_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            if self.isVisible():
                self.hide()
            else:
                self.show()
                self.raise_()
                self.activateWindow()
    
    def _on_main_button_clicked(self):
        """主按钮点击事件"""
        self.logger.info("悬浮球被点击，准备显示主界面")
        self._show_main_window()

    def _show_main_window(self):
        """显示主界面"""
        try:
            if self.main_window is None:
                self.main_window = MainWindow(self.config_manager)

            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()

        except Exception as e:
            self.logger.error(f"显示主界面失败: {e}")

    def _show_config_dialog(self):
        """显示配置对话框"""
        try:
            self.logger.info("显示配置对话框")

            if self.config_dialog is None:
                self.config_dialog = ConfigDialog(self.config_manager, self)
                self.config_dialog.config_saved.connect(self._on_config_saved)

            self.config_dialog.show()
            self.config_dialog.raise_()
            self.config_dialog.activateWindow()

        except Exception as e:
            self.logger.error(f"显示配置对话框失败: {e}")

    def _on_config_saved(self):
        """配置保存后的处理"""
        self.logger.info("配置已保存，重新加载配置")
        # 这里可以添加配置更新后的处理逻辑
    
    def _quit_application(self):
        """退出应用"""
        self.logger.info("用户请求退出应用")
        
        # 保存当前位置
        self._save_position()
        
        # 退出应用
        QApplication.quit()
    
    def _save_position(self):
        """保存悬浮球位置"""
        try:
            pos = self.pos()
            position = {"x": pos.x(), "y": pos.y()}
            
            ui_config = self.config_manager.get_ui_config()
            ui_config["floating_ball"]["position"] = position
            self.config_manager.set_ui_config(ui_config)
            
            self.logger.info(f"悬浮球位置已保存: {position}")
            
        except Exception as e:
            self.logger.error(f"保存悬浮球位置失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.dragging:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            # 保存新位置
            self._save_position()
            event.accept()
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.setWindowOpacity(1.0)  # 鼠标悬停时完全不透明
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        ui_config = self.config_manager.get_ui_config()
        opacity = ui_config.get("floating_ball", {}).get("opacity", 0.8)
        self.setWindowOpacity(opacity)  # 恢复原始透明度
        super().leaveEvent(event)
    
    def closeEvent(self, event):
        """关闭事件"""
        # 隐藏到系统托盘而不是真正关闭
        event.ignore()
        self.hide()
        
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.tray_icon.showMessage(
                "CoreMail AI",
                "应用已最小化到系统托盘",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
