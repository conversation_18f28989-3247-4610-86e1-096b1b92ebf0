#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置对话框模块
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QLineEdit, QPushButton, QSpinBox,
                            QComboBox, QTextEdit, QCheckBox, QGroupBox,
                            QFormLayout, QMessageBox, QSlider, QDoubleSpinBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from src.utils.logger import get_logger
from src.utils.helpers import validate_email


class ConfigDialog(QDialog):
    """配置对话框"""
    
    # 信号
    config_saved = pyqtSignal()
    
    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.logger = get_logger("ConfigDialog")
        
        self.setWindowTitle("CoreMail AI - 设置")
        self.setModal(True)
        self.resize(600, 500)
        
        # 初始化UI
        self._init_ui()
        
        # 加载配置
        self._load_config()
        
        self.logger.info("配置对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 邮箱配置选项卡
        self._create_email_tab()
        
        # AI配置选项卡
        self._create_ai_tab()
        
        # 提示词配置选项卡
        self._create_prompts_tab()
        
        # UI配置选项卡
        self._create_ui_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.test_connection_btn = QPushButton("测试连接")
        self.test_connection_btn.clicked.connect(self._test_connection)
        button_layout.addWidget(self.test_connection_btn)
        
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self._save_config)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _create_email_tab(self):
        """创建邮箱配置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "邮箱配置")
        
        layout = QVBoxLayout()
        tab.setLayout(layout)
        
        # 服务器配置组
        server_group = QGroupBox("服务器配置")
        server_layout = QFormLayout()
        server_group.setLayout(server_layout)
        
        self.server_edit = QLineEdit()
        self.server_edit.setPlaceholderText("例如: imap.coremail.cn")
        server_layout.addRow("服务器地址:", self.server_edit)
        
        self.port_spin = QSpinBox()
        self.port_spin.setRange(1, 65535)
        self.port_spin.setValue(993)
        server_layout.addRow("端口:", self.port_spin)
        
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItems(["IMAP", "POP3"])
        server_layout.addRow("协议:", self.protocol_combo)
        
        self.ssl_check = QCheckBox("使用SSL/TLS")
        self.ssl_check.setChecked(True)
        server_layout.addRow("", self.ssl_check)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 300)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" 秒")
        server_layout.addRow("超时时间:", self.timeout_spin)
        
        layout.addWidget(server_group)
        
        # 账户配置组
        account_group = QGroupBox("账户配置")
        account_layout = QFormLayout()
        account_group.setLayout(account_layout)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("邮箱地址")
        account_layout.addRow("用户名:", self.username_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("邮箱密码")
        account_layout.addRow("密码:", self.password_edit)
        
        layout.addWidget(account_group)
        
        layout.addStretch()
    
    def _create_ai_tab(self):
        """创建AI配置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "AI配置")
        
        layout = QVBoxLayout()
        tab.setLayout(layout)
        
        # 硅基流动配置组
        sf_group = QGroupBox("硅基流动配置")
        sf_layout = QFormLayout()
        sf_group.setLayout(sf_layout)
        
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_edit.setPlaceholderText("输入硅基流动API密钥")
        sf_layout.addRow("API密钥:", self.api_key_edit)
        
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setText("https://api.siliconflow.cn/v1")
        sf_layout.addRow("API地址:", self.base_url_edit)
        
        self.model_combo = QComboBox()
        self.model_combo.setEditable(True)
        self.model_combo.addItems([
            "Qwen/Qwen2.5-72B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct",
            "Qwen/Qwen2.5-14B-Instruct",
            "Qwen/Qwen2.5-7B-Instruct",
            "deepseek-ai/DeepSeek-V2.5",
            "meta-llama/Llama-3.1-70B-Instruct",
            "meta-llama/Llama-3.1-8B-Instruct"
        ])
        sf_layout.addRow("模型:", self.model_combo)
        
        layout.addWidget(sf_group)
        
        # 生成参数组
        params_group = QGroupBox("生成参数")
        params_layout = QFormLayout()
        params_group.setLayout(params_layout)
        
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 8000)
        self.max_tokens_spin.setValue(2000)
        params_layout.addRow("最大令牌数:", self.max_tokens_spin)
        
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setValue(0.7)
        params_layout.addRow("温度:", self.temperature_spin)
        
        layout.addWidget(params_group)
        
        layout.addStretch()
    
    def _create_prompts_tab(self):
        """创建提示词配置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "提示词配置")
        
        layout = QVBoxLayout()
        tab.setLayout(layout)
        
        # 说明标签
        info_label = QLabel("自定义提示词模板，使用 {email_content} 作为邮件内容占位符")
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(info_label)
        
        # 总结提示词
        summary_label = QLabel("邮件总结提示词:")
        summary_label.setFont(QFont("", 10, QFont.Weight.Bold))
        layout.addWidget(summary_label)
        
        self.summary_prompt_edit = QTextEdit()
        self.summary_prompt_edit.setMaximumHeight(120)
        layout.addWidget(self.summary_prompt_edit)
        
        # 待办事项提示词
        todo_label = QLabel("待办事项提取提示词:")
        todo_label.setFont(QFont("", 10, QFont.Weight.Bold))
        layout.addWidget(todo_label)
        
        self.todo_prompt_edit = QTextEdit()
        self.todo_prompt_edit.setMaximumHeight(120)
        layout.addWidget(self.todo_prompt_edit)
        
        # 回复建议提示词
        reply_label = QLabel("回复建议提示词:")
        reply_label.setFont(QFont("", 10, QFont.Weight.Bold))
        layout.addWidget(reply_label)
        
        self.reply_prompt_edit = QTextEdit()
        self.reply_prompt_edit.setMaximumHeight(120)
        layout.addWidget(self.reply_prompt_edit)
        
        # 重置按钮
        reset_btn = QPushButton("重置为默认")
        reset_btn.clicked.connect(self._reset_prompts)
        layout.addWidget(reset_btn)
    
    def _create_ui_tab(self):
        """创建UI配置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "界面配置")
        
        layout = QVBoxLayout()
        tab.setLayout(layout)
        
        # 悬浮球配置组
        ball_group = QGroupBox("悬浮球配置")
        ball_layout = QFormLayout()
        ball_group.setLayout(ball_layout)
        
        self.ball_size_spin = QSpinBox()
        self.ball_size_spin.setRange(40, 120)
        self.ball_size_spin.setValue(60)
        self.ball_size_spin.setSuffix(" px")
        ball_layout.addRow("悬浮球大小:", self.ball_size_spin)
        
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(30, 100)
        self.opacity_slider.setValue(80)
        self.opacity_label = QLabel("80%")
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )
        
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        
        ball_layout.addRow("透明度:", opacity_layout)
        
        layout.addWidget(ball_group)
        
        # 主窗口配置组
        window_group = QGroupBox("主窗口配置")
        window_layout = QFormLayout()
        window_group.setLayout(window_layout)
        
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2000)
        self.window_width_spin.setValue(1200)
        self.window_width_spin.setSuffix(" px")
        window_layout.addRow("窗口宽度:", self.window_width_spin)
        
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1500)
        self.window_height_spin.setValue(800)
        self.window_height_spin.setSuffix(" px")
        window_layout.addRow("窗口高度:", self.window_height_spin)
        
        layout.addWidget(window_group)
        
        layout.addStretch()
    
    def _load_config(self):
        """加载配置"""
        try:
            # 加载邮箱配置
            email_config = self.config_manager.get_email_config()
            self.server_edit.setText(email_config.get("server", ""))
            self.port_spin.setValue(email_config.get("port", 993))
            self.protocol_combo.setCurrentText(email_config.get("protocol", "IMAP"))
            self.ssl_check.setChecked(email_config.get("ssl", True))
            self.timeout_spin.setValue(email_config.get("timeout", 30))
            self.username_edit.setText(email_config.get("username", ""))
            self.password_edit.setText(email_config.get("password", ""))
            
            # 加载AI配置
            sf_config = self.config_manager.get_siliconflow_config()
            self.api_key_edit.setText(sf_config.get("api_key", ""))
            self.base_url_edit.setText(sf_config.get("base_url", "https://api.siliconflow.cn/v1"))
            self.model_combo.setCurrentText(sf_config.get("model", "Qwen/Qwen2.5-72B-Instruct"))
            self.max_tokens_spin.setValue(sf_config.get("max_tokens", 2000))
            self.temperature_spin.setValue(sf_config.get("temperature", 0.7))
            
            # 加载提示词配置
            prompts_config = self.config_manager.get("prompts", {})
            self.summary_prompt_edit.setPlainText(prompts_config.get("summary", ""))
            self.todo_prompt_edit.setPlainText(prompts_config.get("todo", ""))
            self.reply_prompt_edit.setPlainText(prompts_config.get("reply", ""))
            
            # 加载UI配置
            ui_config = self.config_manager.get_ui_config()
            ball_config = ui_config.get("floating_ball", {})
            self.ball_size_spin.setValue(ball_config.get("size", 60))
            opacity = int(ball_config.get("opacity", 0.8) * 100)
            self.opacity_slider.setValue(opacity)
            self.opacity_label.setText(f"{opacity}%")
            
            window_config = ui_config.get("main_window", {})
            self.window_width_spin.setValue(window_config.get("width", 1200))
            self.window_height_spin.setValue(window_config.get("height", 800))
            
            self.logger.info("配置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            QMessageBox.warning(self, "警告", f"加载配置失败: {e}")
    
    def _save_config(self):
        """保存配置"""
        try:
            # 验证邮箱地址
            username = self.username_edit.text().strip()
            if username and not validate_email(username):
                QMessageBox.warning(self, "错误", "请输入有效的邮箱地址")
                return
            
            # 保存邮箱配置
            email_config = {
                "server": self.server_edit.text().strip(),
                "port": self.port_spin.value(),
                "protocol": self.protocol_combo.currentText(),
                "ssl": self.ssl_check.isChecked(),
                "timeout": self.timeout_spin.value(),
                "username": username,
                "password": self.password_edit.text()
            }
            self.config_manager.set_email_config(email_config)
            
            # 保存AI配置
            sf_config = {
                "api_key": self.api_key_edit.text().strip(),
                "base_url": self.base_url_edit.text().strip(),
                "model": self.model_combo.currentText().strip(),
                "max_tokens": self.max_tokens_spin.value(),
                "temperature": self.temperature_spin.value()
            }
            self.config_manager.set_siliconflow_config(sf_config)
            
            # 保存提示词配置
            prompts_config = {
                "summary": self.summary_prompt_edit.toPlainText().strip(),
                "todo": self.todo_prompt_edit.toPlainText().strip(),
                "reply": self.reply_prompt_edit.toPlainText().strip()
            }
            self.config_manager.set("prompts", prompts_config)
            
            # 保存UI配置
            ui_config = self.config_manager.get_ui_config()
            ui_config["floating_ball"] = {
                "size": self.ball_size_spin.value(),
                "opacity": self.opacity_slider.value() / 100.0,
                "position": ui_config.get("floating_ball", {}).get("position", {"x": 100, "y": 100})
            }
            ui_config["main_window"] = {
                "width": self.window_width_spin.value(),
                "height": self.window_height_spin.value(),
                "min_width": 800,
                "min_height": 600
            }
            self.config_manager.set_ui_config(ui_config)
            
            self.logger.info("配置保存成功")
            QMessageBox.information(self, "成功", "配置已保存")
            
            # 发送信号
            self.config_saved.emit()
            
            # 关闭对话框
            self.accept()
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {e}")
    
    def _test_connection(self):
        """测试邮件服务器连接"""
        try:
            # 获取当前配置
            email_config = {
                "server": self.server_edit.text().strip(),
                "port": self.port_spin.value(),
                "protocol": self.protocol_combo.currentText(),
                "ssl": self.ssl_check.isChecked(),
                "timeout": self.timeout_spin.value(),
                "username": self.username_edit.text().strip(),
                "password": self.password_edit.text()
            }
            
            # 验证必填字段
            if not all([email_config["server"], email_config["username"], email_config["password"]]):
                QMessageBox.warning(self, "错误", "请填写完整的服务器地址、用户名和密码")
                return
            
            # 验证邮箱地址
            if not validate_email(email_config["username"]):
                QMessageBox.warning(self, "错误", "请输入有效的邮箱地址")
                return
            
            # 测试连接
            self.test_connection_btn.setText("测试中...")
            self.test_connection_btn.setEnabled(False)
            
            from src.core.email_client import create_email_client
            
            client = create_email_client(email_config)
            if client.connect():
                client.disconnect()
                QMessageBox.information(self, "成功", "邮件服务器连接测试成功！")
            else:
                QMessageBox.warning(self, "失败", "邮件服务器连接测试失败，请检查配置")
            
        except Exception as e:
            self.logger.error(f"测试连接失败: {e}")
            QMessageBox.critical(self, "错误", f"测试连接失败: {e}")
        
        finally:
            self.test_connection_btn.setText("测试连接")
            self.test_connection_btn.setEnabled(True)
    
    def _reset_prompts(self):
        """重置提示词为默认值"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置所有提示词为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 清空当前提示词
            self.summary_prompt_edit.clear()
            self.todo_prompt_edit.clear()
            self.reply_prompt_edit.clear()
