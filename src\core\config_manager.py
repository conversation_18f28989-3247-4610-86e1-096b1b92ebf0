#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet

from src.utils.logger import get_logger
from src.utils.helpers import ensure_dir, generate_key, encrypt_password, decrypt_password


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.logger = get_logger("ConfigManager")
        
        # 配置文件路径
        self.config_dir = Path.home() / ".coremailai"
        self.config_file = self.config_dir / "config.yaml"
        self.key_file = self.config_dir / "key.key"
        
        # 默认配置文件路径
        self.default_config_file = Path("resources/config/default_config.yaml")
        
        # 配置数据
        self.config = {}
        self.encryption_key = None
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化配置管理器"""
        try:
            # 确保配置目录存在
            ensure_dir(self.config_dir)
            
            # 加载或生成加密密钥
            self._load_or_generate_key()
            
            # 加载配置
            self._load_config()
            
            self.logger.info("配置管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"配置管理器初始化失败: {e}")
            raise
    
    def _load_or_generate_key(self):
        """加载或生成加密密钥"""
        try:
            if self.key_file.exists():
                # 加载现有密钥
                with open(self.key_file, 'rb') as f:
                    self.encryption_key = f.read()
                self.logger.info("已加载加密密钥")
            else:
                # 生成新密钥
                self.encryption_key = generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(self.encryption_key)
                # 设置文件权限（仅所有者可读写）
                os.chmod(self.key_file, 0o600)
                self.logger.info("已生成新的加密密钥")
                
        except Exception as e:
            self.logger.error(f"密钥处理失败: {e}")
            raise
    
    def _load_config(self):
        """加载配置"""
        try:
            if self.config_file.exists():
                # 加载用户配置
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                self.logger.info("已加载用户配置")
            else:
                # 加载默认配置
                self._load_default_config()
                # 保存默认配置到用户配置文件
                self.save_config()
                
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            # 尝试加载默认配置
            self._load_default_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        try:
            if self.default_config_file.exists():
                with open(self.default_config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                self.logger.info("已加载默认配置")
            else:
                # 如果默认配置文件不存在，使用硬编码的基本配置
                self.config = self._get_basic_config()
                self.logger.warning("默认配置文件不存在，使用基本配置")
                
        except Exception as e:
            self.logger.error(f"默认配置加载失败: {e}")
            self.config = self._get_basic_config()
    
    def _get_basic_config(self) -> Dict[str, Any]:
        """获取基本配置"""
        return {
            "app": {
                "name": "CoreMail AI",
                "version": "1.0.0",
                "debug": False
            },
            "email": {
                "server": "",
                "port": 993,
                "protocol": "IMAP",
                "username": "",
                "password": "",
                "ssl": True,
                "timeout": 30
            },
            "siliconflow": {
                "api_key": "",
                "base_url": "https://api.siliconflow.cn/v1",
                "model": "Qwen/Qwen2.5-72B-Instruct",
                "max_tokens": 2000,
                "temperature": 0.7
            },
            "ui": {
                "floating_ball": {
                    "size": 60,
                    "opacity": 0.8,
                    "position": {"x": 100, "y": 100}
                },
                "main_window": {
                    "width": 1200,
                    "height": 800,
                    "min_width": 800,
                    "min_height": 600
                }
            },
            "logging": {
                "level": "INFO",
                "file": "logs/app.log",
                "max_size": "10MB",
                "backup_count": 5
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key (str): 配置键，支持点分隔的嵌套键，如 "email.server"
            default (Any): 默认值
        
        Returns:
            Any: 配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key (str): 配置键，支持点分隔的嵌套键
            value (Any): 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            self.logger.info("配置已保存")
            
        except Exception as e:
            self.logger.error(f"配置保存失败: {e}")
            raise
    
    def encrypt_and_save_password(self, password: str) -> str:
        """
        加密并保存密码
        
        Args:
            password (str): 明文密码
        
        Returns:
            str: 加密后的密码
        """
        try:
            encrypted = encrypt_password(password, self.encryption_key)
            return encrypted
        except Exception as e:
            self.logger.error(f"密码加密失败: {e}")
            raise
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """
        解密密码
        
        Args:
            encrypted_password (str): 加密的密码
        
        Returns:
            str: 明文密码
        """
        try:
            decrypted = decrypt_password(encrypted_password, self.encryption_key)
            return decrypted
        except Exception as e:
            self.logger.error(f"密码解密失败: {e}")
            raise
    
    def get_email_config(self) -> Dict[str, Any]:
        """获取邮件配置"""
        email_config = self.get("email", {})
        
        # 如果密码已加密，解密它
        if email_config.get("password"):
            try:
                email_config["password"] = self.decrypt_password(email_config["password"])
            except:
                # 如果解密失败，可能是明文密码，保持原样
                pass
        
        return email_config
    
    def set_email_config(self, config: Dict[str, Any]):
        """
        设置邮件配置
        
        Args:
            config (Dict): 邮件配置
        """
        # 加密密码
        if config.get("password"):
            config["password"] = self.encrypt_and_save_password(config["password"])
        
        self.set("email", config)
        self.save_config()
    
    def get_siliconflow_config(self) -> Dict[str, Any]:
        """获取硅基流动配置"""
        return self.get("siliconflow", {})
    
    def set_siliconflow_config(self, config: Dict[str, Any]):
        """
        设置硅基流动配置
        
        Args:
            config (Dict): 硅基流动配置
        """
        self.set("siliconflow", config)
        self.save_config()
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self.get("ui", {})
    
    def set_ui_config(self, config: Dict[str, Any]):
        """
        设置UI配置
        
        Args:
            config (Dict): UI配置
        """
        self.set("ui", config)
        self.save_config()
