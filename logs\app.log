2025-08-15 14:32:18 | INFO     | __main__:initialize:36 - 正在启动 CoreMail AI 应用...
2025-08-15 14:32:18 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 14:32:18 | INFO     | src.core.config_manager:_load_config:84 - 已加载用户配置
2025-08-15 14:32:18 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 14:32:18 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 14:32:18 | INFO     | __main__:initialize:53 - CoreMail AI 应用初始化完成
2025-08-15 14:32:18 | INFO     | __main__:run:63 - CoreMail AI 应用已启动
2025-08-15 14:32:20 | INFO     | src.core.config_manager:save_config:205 - 配置已保存
2025-08-15 14:32:20 | INFO     | src.ui.floating_ball:_save_position:221 - 悬浮球位置已保存: {'x': 100, 'y': 100}
2025-08-15 14:32:22 | INFO     | src.core.config_manager:save_config:205 - 配置已保存
2025-08-15 14:32:22 | INFO     | src.ui.floating_ball:_save_position:221 - 悬浮球位置已保存: {'x': 100, 'y': 100}
2025-08-15 14:32:22 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:32:22 | INFO     | src.ui.main_window:__init__:337 - 主窗口初始化完成
2025-08-15 14:32:26 | INFO     | src.core.config_manager:save_config:205 - 配置已保存
2025-08-15 14:32:26 | INFO     | src.ui.floating_ball:_save_position:221 - 悬浮球位置已保存: {'x': 100, 'y': 100}
2025-08-15 14:32:29 | INFO     | src.core.config_manager:save_config:205 - 配置已保存
2025-08-15 14:32:29 | INFO     | src.ui.floating_ball:_save_position:221 - 悬浮球位置已保存: {'x': 100, 'y': 100}
2025-08-15 14:32:30 | INFO     | src.core.config_manager:save_config:205 - 配置已保存
2025-08-15 14:32:30 | INFO     | src.ui.floating_ball:_save_position:221 - 悬浮球位置已保存: {'x': 100, 'y': 100}
2025-08-15 14:32:31 | INFO     | src.core.config_manager:save_config:205 - 配置已保存
2025-08-15 14:32:31 | INFO     | src.ui.floating_ball:_save_position:221 - 悬浮球位置已保存: {'x': 100, 'y': 100}
2025-08-15 14:32:32 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:32:37 | INFO     | src.core.email_client:connect:113 - 正在连接到IMAP服务器: test.example.com:993
2025-08-15 14:32:37 | ERROR    | src.core.email_client:connect:129 - IMAP连接失败: [Errno 11001] getaddrinfo failed
2025-08-15 14:32:56 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:33:05 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:35:25 | INFO     | __main__:initialize:36 - 正在启动 CoreMail AI 应用...
2025-08-15 14:35:25 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 14:35:25 | INFO     | src.core.config_manager:_load_config:84 - 已加载用户配置
2025-08-15 14:35:25 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 14:35:25 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 14:35:25 | INFO     | __main__:initialize:53 - CoreMail AI 应用初始化完成
2025-08-15 14:35:25 | INFO     | __main__:run:63 - CoreMail AI 应用已启动
2025-08-15 14:35:38 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:35:38 | INFO     | src.ui.main_window:__init__:337 - 主窗口初始化完成
2025-08-15 14:35:47 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:35:48 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:35:49 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:35:49 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:35:50 | INFO     | src.core.email_client:connect:113 - 正在连接到IMAP服务器: test.example.com:993
2025-08-15 14:35:50 | ERROR    | src.core.email_client:connect:129 - IMAP连接失败: [Errno 11001] getaddrinfo failed
2025-08-15 14:35:56 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:36:01 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:39:26 | INFO     | __main__:initialize:36 - 正在启动 CoreMail AI 应用...
2025-08-15 14:39:26 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 14:39:26 | INFO     | src.core.config_manager:_load_config:84 - 已加载用户配置
2025-08-15 14:39:26 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 14:39:26 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 14:39:26 | INFO     | __main__:initialize:53 - CoreMail AI 应用初始化完成
2025-08-15 14:39:27 | INFO     | __main__:run:63 - CoreMail AI 应用已启动
2025-08-15 14:39:30 | INFO     | src.ui.floating_ball:_on_main_button_clicked:164 - 悬浮球被点击，准备显示主界面
2025-08-15 14:39:30 | INFO     | src.ui.main_window:__init__:337 - 主窗口初始化完成
2025-08-15 14:39:31 | INFO     | src.core.email_client:connect:113 - 正在连接到IMAP服务器: test.example.com:993
2025-08-15 14:39:31 | ERROR    | src.core.email_client:connect:129 - IMAP连接失败: [Errno 11001] getaddrinfo failed
2025-08-15 14:39:34 | ERROR    | src.core.config_manager:decrypt_password:242 - 密码解密失败: 
2025-08-15 14:39:34 | INFO     | src.core.email_client:connect:113 - 正在连接到IMAP服务器: test.example.com:993
2025-08-15 14:39:34 | ERROR    | src.core.email_client:connect:129 - IMAP连接失败: [Errno 11001] getaddrinfo failed
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 14:57:23 | INFO     | src.core.email_client:create_email_client:472 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 14:57:23 | INFO     | src.core.email_client:create_email_client:478 - 使用Coremail客户端
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 14:57:23 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 14:57:23 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 14:58:35 | INFO     | src.core.email_client:create_email_client:472 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 14:58:35 | INFO     | src.core.email_client:create_email_client:478 - 使用Coremail客户端
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 14:58:35 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 14:58:35 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_config:93 - 未找到用户配置文件，使用默认配置
2025-08-15 15:03:21 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:03:21 | ERROR    | src.core.config_manager:decrypt_password:274 - 密码解密失败: 
2025-08-15 15:03:21 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:03:21 | INFO     | src.core.email_client:create_email_client:479 - 使用Coremail客户端
2025-08-15 15:03:21 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mails.dfmc.com.cn/coremail
2025-08-15 15:03:21 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.dfmc.com.cn
2025-08-15 15:03:21 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.dfmc.com.cn, 端口=993, SSL=True
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 15:03:21 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:03:21 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 15:03:33 | INFO     | __main__:initialize:36 - 正在启动 CoreMail AI 应用...
2025-08-15 15:03:33 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:03:33 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:03:33 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:03:33 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:03:33 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 15:03:33 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 15:03:33 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:03:33 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 15:03:33 | INFO     | __main__:initialize:53 - CoreMail AI 应用初始化完成
2025-08-15 15:03:33 | INFO     | __main__:run:63 - CoreMail AI 应用已启动
2025-08-15 15:03:36 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:03:36 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 943, 'y': 328}
2025-08-15 15:03:37 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:03:37 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 1279, 'y': 782}
2025-08-15 15:03:42 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:03:42 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 1282, 'y': 672}
2025-08-15 15:05:15 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:05:15 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 969, 'y': 380}
2025-08-15 15:05:16 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:05:16 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 969, 'y': 380}
2025-08-15 15:05:17 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:05:17 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 1213, 'y': -428}
2025-08-15 15:05:19 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:05:19 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 761, 'y': -750}
2025-08-15 15:05:23 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:05:23 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 624, 'y': 212}
2025-08-15 15:06:39 | INFO     | __main__:initialize:36 - 正在启动 CoreMail AI 应用...
2025-08-15 15:06:39 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:06:39 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:06:39 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:06:39 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:06:39 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 15:06:39 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 15:06:39 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:06:39 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 15:06:39 | INFO     | __main__:initialize:53 - CoreMail AI 应用初始化完成
2025-08-15 15:06:39 | INFO     | __main__:run:63 - CoreMail AI 应用已启动
2025-08-15 15:06:41 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:06:41 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 298, 'y': 214}
2025-08-15 15:06:45 | INFO     | src.ui.floating_ball:_show_config_dialog:192 - 显示配置对话框
2025-08-15 15:06:45 | ERROR    | src.core.config_manager:decrypt_password:274 - 密码解密失败: 
2025-08-15 15:06:45 | INFO     | src.ui.config_dialog:_load_config:334 - 配置加载完成
2025-08-15 15:06:45 | INFO     | src.ui.config_dialog:__init__:39 - 配置对话框初始化完成
2025-08-15 15:07:20 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:20 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:20 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:20 | INFO     | src.ui.config_dialog:_save_config:394 - 配置保存成功
2025-08-15 15:07:21 | INFO     | src.ui.floating_ball:_on_config_saved:207 - 配置已保存，重新加载配置
2025-08-15 15:07:24 | INFO     | src.ui.floating_ball:_show_config_dialog:192 - 显示配置对话框
2025-08-15 15:07:27 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:27 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:27 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:27 | INFO     | src.ui.config_dialog:_save_config:394 - 配置保存成功
2025-08-15 15:07:28 | INFO     | src.ui.floating_ball:_on_config_saved:207 - 配置已保存，重新加载配置
2025-08-15 15:07:31 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:31 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 298, 'y': 214}
2025-08-15 15:07:32 | INFO     | src.ui.floating_ball:_show_config_dialog:192 - 显示配置对话框
2025-08-15 15:07:53 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:53 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:53 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:07:53 | INFO     | src.ui.config_dialog:_save_config:394 - 配置保存成功
2025-08-15 15:07:54 | INFO     | src.ui.floating_ball:_on_config_saved:207 - 配置已保存，重新加载配置
2025-08-15 15:08:03 | INFO     | src.ui.floating_ball:_show_config_dialog:192 - 显示配置对话框
2025-08-15 15:08:05 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:05 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 298, 'y': 214}
2025-08-15 15:08:07 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:07 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 298, 'y': 214}
2025-08-15 15:08:07 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:07 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 298, 'y': 214}
2025-08-15 15:08:08 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:08 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 298, 'y': 214}
2025-08-15 15:08:08 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:08 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 298, 'y': 214}
2025-08-15 15:08:08 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:08 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 908, 'y': 483}
2025-08-15 15:08:09 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:09 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 917, 'y': 493}
2025-08-15 15:08:09 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:09 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 909, 'y': 546}
2025-08-15 15:08:10 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:10 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 1068, 'y': 543}
2025-08-15 15:08:14 | INFO     | src.ui.main_window:__init__:337 - 主窗口初始化完成
2025-08-15 15:08:17 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: IMAP, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:08:17 | INFO     | src.core.email_client:create_email_client:476 - 使用标准IMAP客户端
2025-08-15 15:08:17 | INFO     | src.core.email_client:connect:107 - IMAP连接参数 - 服务器: https://mails.dfmc.com.cn/coremail, 端口: 993, SSL: True, 用户名: <EMAIL>
2025-08-15 15:08:17 | INFO     | src.core.email_client:connect:115 - 正在连接到IMAP服务器: https://mails.dfmc.com.cn/coremail:993 (SSL: True)
2025-08-15 15:08:17 | INFO     | src.core.email_client:connect:119 - 使用SSL连接
2025-08-15 15:08:17 | ERROR    | src.core.email_client:connect:135 - IMAP连接失败: [Errno 11001] getaddrinfo failed
2025-08-15 15:08:25 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:08:25 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 227, 'y': 86}
2025-08-15 15:12:24 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:12:24 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 242, 'y': 718}
2025-08-15 15:13:07 | INFO     | src.ui.floating_ball:_show_config_dialog:192 - 显示配置对话框
2025-08-15 15:13:51 | INFO     | src.ui.floating_ball:_quit_application:248 - 用户请求退出应用
2025-08-15 15:13:51 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:13:51 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 242, 'y': 718}
2025-08-15 15:13:51 | INFO     | __main__:cleanup:78 - 正在关闭 CoreMail AI 应用...
2025-08-15 15:14:35 | INFO     | __main__:initialize:36 - 正在启动 CoreMail AI 应用...
2025-08-15 15:14:35 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:14:35 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:14:35 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:14:35 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:14:35 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 15:14:35 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 15:14:35 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:14:35 | INFO     | src.ui.floating_ball:__init__:43 - 悬浮球界面初始化完成
2025-08-15 15:14:35 | INFO     | __main__:initialize:53 - CoreMail AI 应用初始化完成
2025-08-15 15:14:35 | INFO     | __main__:run:63 - CoreMail AI 应用已启动
2025-08-15 15:14:37 | INFO     | src.core.config_manager:save_config:237 - 配置已保存
2025-08-15 15:14:37 | INFO     | src.ui.floating_ball:_save_position:266 - 悬浮球位置已保存: {'x': 242, 'y': 718}
2025-08-15 15:14:41 | INFO     | src.ui.main_window:__init__:337 - 主窗口初始化完成
2025-08-15 15:14:42 | ERROR    | src.core.config_manager:decrypt_password:274 - 密码解密失败: 
2025-08-15 15:14:42 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:14:42 | INFO     | src.core.email_client:create_email_client:479 - 使用Coremail客户端
2025-08-15 15:14:42 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mails.dfmc.com.cn/coremail
2025-08-15 15:14:42 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.dfmc.com.cn
2025-08-15 15:14:42 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.dfmc.com.cn, 端口=993, SSL=True
2025-08-15 15:14:42 | INFO     | src.core.email_client:connect:440 - 正在连接到Coremail服务器...
2025-08-15 15:14:42 | INFO     | src.core.email_client:connect:107 - IMAP连接参数 - 服务器: imap.dfmc.com.cn, 端口: 993, SSL: True, 用户名: <EMAIL>
2025-08-15 15:14:42 | INFO     | src.core.email_client:connect:115 - 正在连接到IMAP服务器: imap.dfmc.com.cn:993 (SSL: True)
2025-08-15 15:14:42 | INFO     | src.core.email_client:connect:119 - 使用SSL连接
2025-08-15 15:14:42 | ERROR    | src.core.email_client:connect:135 - IMAP连接失败: [Errno 11001] getaddrinfo failed
2025-08-15 15:14:42 | ERROR    | src.core.email_client:connect:448 - Coremail服务器连接失败
2025-08-15 15:18:19 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:18:19 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:18:19 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:18:19 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:18:19 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 15:18:19 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 15:18:19 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:18:19 | ERROR    | src.core.config_manager:decrypt_password:274 - 密码解密失败: 
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:479 - 使用Coremail客户端
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mails.dfmc.com.cn/coremail
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.dfmc.com.cn
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.dfmc.com.cn, 端口=993, SSL=True
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:479 - 使用Coremail客户端
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mails.dfmc.com.cn/coremail
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.dfmc.com.cn
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.dfmc.com.cn, 端口=993, SSL=True
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: IMAP, 服务器: imap.gmail.com
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:476 - 使用标准IMAP客户端
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mail.example.com/coremail
2025-08-15 15:18:19 | INFO     | src.core.email_client:create_email_client:479 - 使用Coremail客户端
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mail.example.com/coremail
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.example.com
2025-08-15 15:18:19 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.example.com, 端口=993, SSL=True
2025-08-15 15:18:20 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:18:20 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:18:20 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:18:20 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:18:20 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 15:18:20 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 15:18:20 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:18:20 | ERROR    | src.core.config_manager:decrypt_password:274 - 密码解密失败: 
2025-08-15 15:18:20 | INFO     | src.core.email_client:create_email_client:473 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:18:20 | INFO     | src.core.email_client:create_email_client:479 - 使用Coremail客户端
2025-08-15 15:18:20 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mails.dfmc.com.cn/coremail
2025-08-15 15:18:20 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.dfmc.com.cn
2025-08-15 15:18:20 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.dfmc.com.cn, 端口=993, SSL=True
2025-08-15 15:20:24 | INFO     | src.core.config_manager:_load_or_generate_key:63 - 已加载加密密钥
2025-08-15 15:20:24 | INFO     | src.core.config_manager:_load_default_config:105 - 尝试加载默认配置文件: resources\config\default_config.yaml
2025-08-15 15:20:24 | INFO     | src.core.config_manager:_load_default_config:113 - 已加载默认配置 - 邮件服务器: https://mails.dfmc.com.cn/coremail, 协议: Coremail
2025-08-15 15:20:24 | INFO     | src.core.config_manager:_load_default_config:117 - 硅基流动配置 - API密钥: 未设置, 模型: Qwen/Qwen2.5-72B-Instruct
2025-08-15 15:20:24 | INFO     | src.core.config_manager:_load_config:85 - 发现用户配置文件，正在合并配置...
2025-08-15 15:20:24 | INFO     | src.core.config_manager:_load_config:91 - 已合并用户配置和默认配置
2025-08-15 15:20:24 | INFO     | src.core.config_manager:_initialize:50 - 配置管理器初始化完成
2025-08-15 15:20:24 | ERROR    | src.core.config_manager:decrypt_password:274 - 密码解密失败: 
2025-08-15 15:20:24 | INFO     | src.core.email_client:create_email_client:474 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:20:24 | INFO     | src.core.email_client:create_email_client:480 - 使用Coremail客户端
2025-08-15 15:20:24 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mails.dfmc.com.cn/coremail
2025-08-15 15:20:24 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.dfmc.com.cn
2025-08-15 15:20:24 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.dfmc.com.cn, 端口=993, SSL=True
2025-08-15 15:20:24 | INFO     | src.core.email_client:connect:440 - 🔗 [CoreMailClient] 正在连接到Coremail服务器...
2025-08-15 15:20:24 | INFO     | src.core.email_client:connect:441 - 🔗 [CoreMailClient] 使用服务器: imap.dfmc.com.cn:993
2025-08-15 15:20:24 | INFO     | src.core.email_client:connect:107 - IMAP连接参数 - 服务器: imap.dfmc.com.cn, 端口: 993, SSL: True, 用户名: <EMAIL>
2025-08-15 15:20:24 | INFO     | src.core.email_client:connect:115 - 正在连接到IMAP服务器: imap.dfmc.com.cn:993 (SSL: True)
2025-08-15 15:20:24 | INFO     | src.core.email_client:connect:119 - 使用SSL连接
2025-08-15 15:20:24 | ERROR    | src.core.email_client:connect:135 - IMAP连接失败: [Errno 11001] getaddrinfo failed
2025-08-15 15:20:24 | ERROR    | src.core.email_client:connect:449 - ❌ [CoreMailClient] Coremail服务器连接失败
2025-08-15 15:20:24 | INFO     | src.core.email_client:create_email_client:474 - 创建邮件客户端 - 协议: COREMAIL, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:20:24 | INFO     | src.core.email_client:create_email_client:480 - 使用Coremail客户端
2025-08-15 15:20:24 | INFO     | src.core.email_client:_process_coremail_config:392 - 原始Coremail服务器配置: https://mails.dfmc.com.cn/coremail
2025-08-15 15:20:24 | INFO     | src.core.email_client:_process_coremail_config:416 - 转换后的IMAP服务器地址: imap.dfmc.com.cn
2025-08-15 15:20:24 | INFO     | src.core.email_client:_process_coremail_config:433 - Coremail配置处理完成: 服务器=imap.dfmc.com.cn, 端口=993, SSL=True
2025-08-15 15:20:24 | INFO     | src.core.email_client:create_email_client:474 - 创建邮件客户端 - 协议: IMAP, 服务器: https://mails.dfmc.com.cn/coremail
2025-08-15 15:20:24 | INFO     | src.core.email_client:create_email_client:477 - 使用标准IMAP客户端
