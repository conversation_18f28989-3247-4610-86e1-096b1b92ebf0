#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoreMail AI 启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: Python {sys.version}")
        return False
    return True


def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        "PyQt6",
        "requests",
        "pyyaml",
        "loguru",
        "cryptography"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (缺失)")
            missing_packages.append(package)
    
    return missing_packages


def install_dependencies(missing_packages):
    """安装缺失的依赖包"""
    if not missing_packages:
        return True
    
    print(f"\n📦 发现 {len(missing_packages)} 个缺失的依赖包")
    print("正在安装...")
    
    try:
        # 尝试安装requirements.txt中的所有依赖
        requirements_file = Path("requirements.txt")
        if requirements_file.exists():
            cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 依赖包安装成功")
                return True
            else:
                print(f"❌ 依赖包安装失败: {result.stderr}")
                return False
        else:
            print("❌ 未找到requirements.txt文件")
            return False
            
    except Exception as e:
        print(f"❌ 安装依赖包时出错: {e}")
        return False


def run_tests():
    """运行测试"""
    print("\n🧪 运行基础测试...")
    
    try:
        test_file = Path("tests/test_basic.py")
        if test_file.exists():
            cmd = [sys.executable, str(test_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 基础测试通过")
                return True
            else:
                print("❌ 基础测试失败")
                print(result.stdout)
                print(result.stderr)
                return False
        else:
            print("⚠️  未找到测试文件，跳过测试")
            return True
            
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False


def start_application():
    """启动应用程序"""
    print("\n🚀 启动 CoreMail AI...")
    
    try:
        main_file = Path("src/main.py")
        if main_file.exists():
            # 直接运行主程序
            os.chdir(Path(__file__).parent)
            subprocess.run([sys.executable, str(main_file)])
        else:
            print("❌ 未找到主程序文件 src/main.py")
            return False
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        return True
    except Exception as e:
        print(f"❌ 启动应用程序时出错: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("🎯 CoreMail AI 邮件智能分析应用")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查依赖包
    missing_packages = check_dependencies()
    
    # 如果有缺失的依赖包，尝试安装
    if missing_packages:
        install_choice = input(f"\n是否自动安装缺失的依赖包? (y/n): ").lower().strip()
        if install_choice in ['y', 'yes', '是']:
            if not install_dependencies(missing_packages):
                print("\n❌ 依赖包安装失败，请手动安装后重试")
                print("手动安装命令: pip install -r requirements.txt")
                sys.exit(1)
        else:
            print("\n❌ 缺少必要的依赖包，程序无法运行")
            print("请运行以下命令安装依赖包:")
            print("pip install -r requirements.txt")
            sys.exit(1)
    
    # 运行测试
    test_choice = input("\n是否运行基础测试? (y/n): ").lower().strip()
    if test_choice in ['y', 'yes', '是']:
        if not run_tests():
            print("\n⚠️  测试失败，但仍可以尝试运行应用程序")
    
    # 启动应用程序
    start_choice = input("\n是否启动应用程序? (y/n): ").lower().strip()
    if start_choice in ['y', 'yes', '是']:
        start_application()
    else:
        print("\n👋 再见！")


if __name__ == "__main__":
    main()
