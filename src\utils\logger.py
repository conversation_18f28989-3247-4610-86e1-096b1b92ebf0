#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
"""

import os
import sys
from pathlib import Path
from loguru import logger


def setup_logger(log_level="INFO", log_file=None):
    """
    设置日志配置
    
    Args:
        log_level (str): 日志级别
        log_file (str): 日志文件路径
    
    Returns:
        logger: 配置好的日志对象
    """
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 添加文件输出
    if log_file is None:
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        log_file = log_dir / "app.log"
    
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    return logger


def get_logger(name=None):
    """
    获取日志对象
    
    Args:
        name (str): 日志名称
    
    Returns:
        logger: 日志对象
    """
    if name:
        return logger.bind(name=name)
    return logger
