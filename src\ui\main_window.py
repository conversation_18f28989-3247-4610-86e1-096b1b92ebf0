#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QSplitter, QListWidget, QListWidgetItem, QTextEdit,
                            QPushButton, QLabel, QProgressBar, QTabWidget,
                            QGroupBox, QScrollArea, QFrame, QMessageBox,
                            QComboBox, QCheckBox, QSpinBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPalette

from src.utils.logger import get_logger
from src.core.email_client import create_email_client, EmailMessage
from src.core.ai_analyzer import EmailAnalyzer, AnalysisType, AnalysisResult


class EmailListWidget(QListWidget):
    """邮件列表控件"""
    
    def __init__(self):
        super().__init__()
        self.setAlternatingRowColors(True)
        self.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)
    
    def add_email_item(self, email_msg: EmailMessage):
        """添加邮件项"""
        item = QListWidgetItem()
        
        # 创建邮件项显示文本
        sender_name = email_msg.sender_name or email_msg.sender_email or "未知发件人"
        subject = email_msg.subject or "无主题"
        date_str = email_msg.date.strftime("%m-%d %H:%M") if email_msg.date else ""
        
        item_text = f"{sender_name}\n{subject}\n{date_str}"
        item.setText(item_text)
        
        # 存储邮件数据
        item.setData(Qt.ItemDataRole.UserRole, email_msg)
        
        self.addItem(item)


class AnalysisResultWidget(QWidget):
    """分析结果显示控件"""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 邮件总结选项卡
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        self.tab_widget.addTab(self.summary_text, "📝 邮件总结")
        
        # 待办事项选项卡
        self.todo_text = QTextEdit()
        self.todo_text.setReadOnly(True)
        self.tab_widget.addTab(self.todo_text, "✅ 待办事项")
        
        # 回复建议选项卡
        self.reply_text = QTextEdit()
        self.reply_text.setReadOnly(True)
        self.tab_widget.addTab(self.reply_text, "💬 回复建议")
        
        # 设置样式
        self._set_style()
    
    def _set_style(self):
        """设置样式"""
        style = """
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
                line-height: 1.5;
                background-color: white;
            }
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                background-color: #f5f5f5;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #1976d2;
            }
        """
        self.setStyleSheet(style)
    
    def update_results(self, results: list):
        """更新分析结果"""
        for result in results:
            if result.analysis_type == AnalysisType.SUMMARY:
                self.summary_text.setPlainText(result.content)
            elif result.analysis_type == AnalysisType.TODO:
                self.todo_text.setPlainText(result.content)
            elif result.analysis_type == AnalysisType.REPLY:
                self.reply_text.setPlainText(result.content)
    
    def clear_results(self):
        """清空结果"""
        self.summary_text.clear()
        self.todo_text.clear()
        self.reply_text.clear()


class EmailWorker(QThread):
    """邮件处理工作线程"""
    
    # 信号
    emails_loaded = pyqtSignal(list)
    analysis_completed = pyqtSignal(str, list)  # uid, results
    batch_analysis_completed = pyqtSignal(dict)  # {uid: results}
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int, str)
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger("EmailWorker")
        self.email_client = None
        self.email_analyzer = None
        self.current_task = None
        self.task_data = None
    
    def load_emails(self, folder="INBOX", limit=50):
        """加载邮件任务"""
        self.current_task = "load_emails"
        self.task_data = {"folder": folder, "limit": limit}
        self.start()
    
    def analyze_email(self, email_msg: EmailMessage, analysis_types: list):
        """分析邮件任务"""
        self.current_task = "analyze_email"
        self.task_data = {"email_msg": email_msg, "analysis_types": analysis_types}
        self.start()

    def batch_analyze_emails(self, emails: list, analysis_types: list):
        """批量分析邮件任务"""
        self.current_task = "batch_analyze"
        self.task_data = {"emails": emails, "analysis_types": analysis_types}
        self.start()
    
    def run(self):
        """运行工作线程"""
        try:
            if self.current_task == "load_emails":
                self._load_emails()
            elif self.current_task == "analyze_email":
                self._analyze_email()
            elif self.current_task == "batch_analyze":
                self._batch_analyze_emails()
        except Exception as e:
            self.logger.error(f"工作线程执行失败: {e}")
            self.error_occurred.emit(str(e))
    
    def _load_emails(self):
        """加载邮件"""
        try:
            self.progress_updated.emit(10, "正在连接邮件服务器...")
            
            # 获取邮件配置
            email_config = self.config_manager.get_email_config()
            if not email_config.get("server"):
                raise Exception("邮件服务器未配置")
            
            # 创建邮件客户端
            self.email_client = create_email_client(email_config)
            
            self.progress_updated.emit(30, "正在连接...")
            if not self.email_client.connect():
                raise Exception("连接邮件服务器失败")
            
            self.progress_updated.emit(50, "正在获取邮件列表...")
            
            # 获取邮件
            folder = self.task_data["folder"]
            limit = self.task_data["limit"]
            emails = self.email_client.get_messages(folder, limit)
            
            self.progress_updated.emit(90, "邮件加载完成")
            
            # 断开连接
            self.email_client.disconnect()
            
            self.progress_updated.emit(100, f"成功加载 {len(emails)} 封邮件")
            self.emails_loaded.emit(emails)
            
        except Exception as e:
            self.error_occurred.emit(f"加载邮件失败: {e}")
    
    def _analyze_email(self):
        """分析邮件"""
        try:
            email_msg = self.task_data["email_msg"]
            analysis_types = self.task_data["analysis_types"]
            
            self.progress_updated.emit(10, "正在初始化AI分析器...")
            
            # 创建分析器
            self.email_analyzer = EmailAnalyzer(self.config_manager)
            
            results = []
            total_types = len(analysis_types)
            
            for i, analysis_type in enumerate(analysis_types):
                progress = 20 + (i * 60 // total_types)
                type_name = {
                    AnalysisType.SUMMARY: "邮件总结",
                    AnalysisType.TODO: "待办事项提取",
                    AnalysisType.REPLY: "回复建议生成"
                }.get(analysis_type, "分析")
                
                self.progress_updated.emit(progress, f"正在进行{type_name}...")
                
                result = self.email_analyzer.analyze_email(email_msg, analysis_type)
                results.append(result)
            
            self.progress_updated.emit(100, "分析完成")
            self.analysis_completed.emit(email_msg.uid, results)
            
        except Exception as e:
            self.error_occurred.emit(f"邮件分析失败: {e}")

    def _batch_analyze_emails(self):
        """批量分析邮件"""
        try:
            emails = self.task_data["emails"]
            analysis_types = self.task_data["analysis_types"]

            self.progress_updated.emit(5, "正在初始化AI分析器...")

            # 创建分析器
            self.email_analyzer = EmailAnalyzer(self.config_manager)

            # 批量分析
            self.progress_updated.emit(10, "开始批量分析...")

            all_results = {}
            total_emails = len(emails)

            for i, email_msg in enumerate(emails):
                try:
                    # 更新进度
                    progress = 10 + (i * 80 // total_emails)
                    self.progress_updated.emit(
                        progress,
                        f"正在分析第 {i+1}/{total_emails} 封邮件..."
                    )

                    # 分析单封邮件
                    email_results = []
                    for analysis_type in analysis_types:
                        result = self.email_analyzer.analyze_email(email_msg, analysis_type)
                        email_results.append(result)

                    all_results[email_msg.uid] = email_results

                except Exception as e:
                    self.logger.error(f"分析邮件 {email_msg.uid} 失败: {e}")
                    # 创建错误结果
                    error_results = []
                    for analysis_type in analysis_types:
                        error_result = AnalysisResult(
                            analysis_type=analysis_type,
                            content=f"分析失败: {str(e)}",
                            error=str(e)
                        )
                        error_results.append(error_result)
                    all_results[email_msg.uid] = error_results

            self.progress_updated.emit(100, f"批量分析完成，共处理 {len(emails)} 封邮件")
            self.batch_analysis_completed.emit(all_results)

        except Exception as e:
            self.error_occurred.emit(f"批量分析失败: {e}")


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger("MainWindow")
        
        # 工作线程
        self.email_worker = EmailWorker(config_manager)
        self.email_worker.emails_loaded.connect(self._on_emails_loaded)
        self.email_worker.analysis_completed.connect(self._on_analysis_completed)
        self.email_worker.batch_analysis_completed.connect(self._on_batch_analysis_completed)
        self.email_worker.error_occurred.connect(self._on_error_occurred)
        self.email_worker.progress_updated.connect(self._on_progress_updated)
        
        # 当前选中的邮件
        self.current_email = None
        
        # 初始化UI
        self._init_ui()
        
        self.logger.info("主窗口初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 获取UI配置
        ui_config = self.config_manager.get_ui_config()
        window_config = ui_config.get("main_window", {})
        
        # 设置窗口属性
        self.setWindowTitle("CoreMail AI - 邮件智能分析")
        self.resize(window_config.get("width", 1200), window_config.get("height", 800))
        self.setMinimumSize(window_config.get("min_width", 800), window_config.get("min_height", 600))
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 工具栏
        self._create_toolbar(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧邮件列表
        self._create_email_list(splitter)
        
        # 右侧分析结果
        self._create_analysis_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
        
        # 状态栏
        self._create_status_bar()
        
        # 设置样式
        self._set_style()
    
    def _create_toolbar(self, layout):
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新邮件")
        self.refresh_btn.clicked.connect(self._refresh_emails)
        toolbar_layout.addWidget(self.refresh_btn)
        
        # 文件夹选择
        toolbar_layout.addWidget(QLabel("文件夹:"))
        self.folder_combo = QComboBox()
        self.folder_combo.addItems(["INBOX", "Sent", "Drafts", "Trash"])
        toolbar_layout.addWidget(self.folder_combo)
        
        # 邮件数量限制
        toolbar_layout.addWidget(QLabel("数量:"))
        self.limit_spin = QSpinBox()
        self.limit_spin.setRange(10, 200)
        self.limit_spin.setValue(50)
        toolbar_layout.addWidget(self.limit_spin)
        
        toolbar_layout.addStretch()
        
        # 分析选项
        self.auto_analyze_check = QCheckBox("自动分析")
        self.auto_analyze_check.setChecked(True)
        toolbar_layout.addWidget(self.auto_analyze_check)
        
        # 分析按钮
        self.analyze_btn = QPushButton("🤖 分析选中")
        self.analyze_btn.clicked.connect(self._analyze_current_email)
        self.analyze_btn.setEnabled(False)
        toolbar_layout.addWidget(self.analyze_btn)

        # 批量分析按钮
        self.batch_analyze_btn = QPushButton("📊 批量分析")
        self.batch_analyze_btn.clicked.connect(self._batch_analyze_emails)
        self.batch_analyze_btn.setEnabled(False)
        toolbar_layout.addWidget(self.batch_analyze_btn)
        
        layout.addLayout(toolbar_layout)
    
    def _create_email_list(self, splitter):
        """创建邮件列表"""
        # 邮件列表容器
        email_container = QWidget()
        email_layout = QVBoxLayout()
        email_container.setLayout(email_layout)
        
        # 标题
        email_label = QLabel("📧 邮件列表")
        email_label.setFont(QFont("", 12, QFont.Weight.Bold))
        email_layout.addWidget(email_label)
        
        # 邮件列表
        self.email_list = EmailListWidget()
        self.email_list.itemClicked.connect(self._on_email_selected)
        email_layout.addWidget(self.email_list)
        
        splitter.addWidget(email_container)
    
    def _create_analysis_panel(self, splitter):
        """创建分析面板"""
        # 分析面板容器
        analysis_container = QWidget()
        analysis_layout = QVBoxLayout()
        analysis_container.setLayout(analysis_layout)
        
        # 标题
        analysis_label = QLabel("🤖 AI分析结果")
        analysis_label.setFont(QFont("", 12, QFont.Weight.Bold))
        analysis_layout.addWidget(analysis_label)
        
        # 分析结果显示
        self.analysis_result = AnalysisResultWidget()
        analysis_layout.addWidget(self.analysis_result)
        
        splitter.addWidget(analysis_container)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
    
    def _set_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e3f2fd;
                border-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #bbdefb;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999;
                border-color: #ddd;
            }
            QLabel {
                color: #333;
            }
            QComboBox, QSpinBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
            }
        """)
    
    def _refresh_emails(self):
        """刷新邮件"""
        if self.email_worker.isRunning():
            QMessageBox.information(self, "提示", "正在处理中，请稍候...")
            return
        
        folder = self.folder_combo.currentText()
        limit = self.limit_spin.value()
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_btn.setEnabled(False)
        
        self.email_worker.load_emails(folder, limit)
    
    def _analyze_current_email(self):
        """分析当前选中的邮件"""
        if not self.current_email:
            QMessageBox.warning(self, "警告", "请先选择一封邮件")
            return
        
        if self.email_worker.isRunning():
            QMessageBox.information(self, "提示", "正在处理中，请稍候...")
            return
        
        # 分析类型
        analysis_types = [AnalysisType.SUMMARY, AnalysisType.TODO, AnalysisType.REPLY]
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.analyze_btn.setEnabled(False)
        
        self.email_worker.analyze_email(self.current_email, analysis_types)

    def _batch_analyze_emails(self):
        """批量分析邮件"""
        if self.email_list.count() == 0:
            QMessageBox.warning(self, "警告", "没有邮件可以分析")
            return

        if self.email_worker.isRunning():
            QMessageBox.information(self, "提示", "正在处理中，请稍候...")
            return

        # 获取所有邮件
        emails = []
        for i in range(self.email_list.count()):
            item = self.email_list.item(i)
            email_msg = item.data(Qt.ItemDataRole.UserRole)
            emails.append(email_msg)

        # 确认批量分析
        reply = QMessageBox.question(
            self, "确认",
            f"确定要分析 {len(emails)} 封邮件吗？这可能需要较长时间。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 分析类型
        analysis_types = [AnalysisType.SUMMARY, AnalysisType.TODO, AnalysisType.REPLY]

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.batch_analyze_btn.setEnabled(False)
        self.analyze_btn.setEnabled(False)
        self.refresh_btn.setEnabled(False)

        self.email_worker.batch_analyze_emails(emails, analysis_types)
    
    def _on_email_selected(self, item):
        """邮件选中事件"""
        email_msg = item.data(Qt.ItemDataRole.UserRole)
        self.current_email = email_msg
        self.analyze_btn.setEnabled(True)
        
        # 清空之前的分析结果
        self.analysis_result.clear_results()
        
        # 如果启用自动分析，则开始分析
        if self.auto_analyze_check.isChecked():
            QTimer.singleShot(100, self._analyze_current_email)
    
    def _on_emails_loaded(self, emails):
        """邮件加载完成"""
        self.email_list.clear()

        for email_msg in emails:
            self.email_list.add_email_item(email_msg)

        self.progress_bar.setVisible(False)
        self.refresh_btn.setEnabled(True)
        self.batch_analyze_btn.setEnabled(len(emails) > 0)
        self.status_label.setText(f"已加载 {len(emails)} 封邮件")
    
    def _on_analysis_completed(self, uid, results):
        """分析完成"""
        self.analysis_result.update_results(results)
        
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)
        self.status_label.setText("分析完成")

    def _on_batch_analysis_completed(self, all_results):
        """批量分析完成"""
        self.progress_bar.setVisible(False)
        self.batch_analyze_btn.setEnabled(True)
        self.analyze_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)

        # 显示批量分析结果摘要
        total_emails = len(all_results)
        success_count = 0
        error_count = 0

        for results in all_results.values():
            has_error = any(result.error for result in results)
            if has_error:
                error_count += 1
            else:
                success_count += 1

        message = f"批量分析完成！\n成功: {success_count} 封\n失败: {error_count} 封"
        QMessageBox.information(self, "批量分析完成", message)

        self.status_label.setText(f"批量分析完成 - 成功: {success_count}, 失败: {error_count}")
    
    def _on_error_occurred(self, error_msg):
        """错误处理"""
        self.progress_bar.setVisible(False)
        self.refresh_btn.setEnabled(True)
        self.analyze_btn.setEnabled(True)
        self.batch_analyze_btn.setEnabled(self.email_list.count() > 0)
        self.status_label.setText("操作失败")

        QMessageBox.critical(self, "错误", error_msg)
    
    def _on_progress_updated(self, value, message):
        """进度更新"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
