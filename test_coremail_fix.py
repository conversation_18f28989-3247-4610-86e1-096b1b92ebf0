#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CoreMail修复的脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager
from src.core.email_client import create_email_client
from src.utils.logger import setup_logger


def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("🔧 测试配置加载")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger()
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 获取邮件配置
        email_config = config_manager.get_email_config()
        
        print(f"📧 邮件配置:")
        print(f"   服务器: {email_config.get('server')}")
        print(f"   端口: {email_config.get('port')}")
        print(f"   协议: {email_config.get('protocol')}")
        print(f"   SSL: {email_config.get('ssl')}")
        print(f"   用户名: {email_config.get('username')}")
        print(f"   密码: {'已设置' if email_config.get('password') else '未设置'}")
        
        # 获取硅基流动配置
        sf_config = config_manager.get_siliconflow_config()
        
        print(f"\n🤖 硅基流动配置:")
        print(f"   API密钥: {'已设置' if sf_config.get('api_key') else '未设置'}")
        print(f"   API地址: {sf_config.get('base_url')}")
        print(f"   模型: {sf_config.get('model')}")
        
        return email_config, sf_config
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return None, None


def test_email_client_creation(email_config):
    """测试邮件客户端创建"""
    print("\n" + "=" * 60)
    print("📧 测试邮件客户端创建")
    print("=" * 60)
    
    if not email_config:
        print("❌ 无法测试邮件客户端创建，配置为空")
        return None
    
    try:
        # 创建邮件客户端
        client = create_email_client(email_config)
        
        print(f"✅ 邮件客户端创建成功")
        print(f"   客户端类型: {type(client).__name__}")
        print(f"   协议: {email_config.get('protocol')}")
        
        return client
        
    except Exception as e:
        print(f"❌ 邮件客户端创建失败: {e}")
        return None


def test_coremail_config_processing():
    """测试Coremail配置处理"""
    print("\n" + "=" * 60)
    print("🔧 测试Coremail配置处理")
    print("=" * 60)
    
    from src.core.email_client import CoreMailClient
    
    # 测试配置
    test_configs = [
        {
            "server": "https://mails.dfmc.com.cn/coremail",
            "protocol": "Coremail",
            "username": "<EMAIL>",
            "password": "test123"
        },
        {
            "server": "https://mail.example.com/coremail",
            "protocol": "Coremail",
            "username": "<EMAIL>",
            "password": "test123"
        },
        {
            "server": "imap.example.com",
            "protocol": "Coremail",
            "username": "<EMAIL>",
            "password": "test123"
        }
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n📋 测试配置 {i}:")
        print(f"   原始服务器: {config['server']}")
        
        try:
            # 创建CoreMail客户端（不实际连接）
            client = CoreMailClient(config)
            processed_config = client.config
            
            print(f"   处理后服务器: {processed_config.get('server')}")
            print(f"   端口: {processed_config.get('port')}")
            print(f"   SSL: {processed_config.get('ssl')}")
            print(f"   ✅ 配置处理成功")
            
        except Exception as e:
            print(f"   ❌ 配置处理失败: {e}")


def test_connection_simulation(client):
    """模拟连接测试（不实际连接）"""
    print("\n" + "=" * 60)
    print("🔗 模拟连接测试")
    print("=" * 60)
    
    if not client:
        print("❌ 无法进行连接测试，客户端为空")
        return
    
    print(f"📡 客户端类型: {type(client).__name__}")
    print(f"📡 服务器配置: {client.config.get('server')}:{client.config.get('port')}")
    print(f"📡 SSL配置: {client.config.get('ssl')}")
    print(f"📡 协议: {client.config.get('protocol')}")
    
    print("\n⚠️  注意: 这只是配置验证，未进行实际网络连接")
    print("   如需测试实际连接，请确保:")
    print("   1. 网络连接正常")
    print("   2. 服务器地址正确")
    print("   3. 用户名密码正确")


def main():
    """主函数"""
    print("🎯 CoreMail AI 修复验证测试")
    print("测试内容:")
    print("1. 配置文件加载")
    print("2. 邮件客户端创建")
    print("3. Coremail配置处理")
    print("4. 连接参数验证")
    
    # 测试配置加载
    email_config, sf_config = test_config_loading()
    
    # 测试邮件客户端创建
    client = test_email_client_creation(email_config)
    
    # 测试Coremail配置处理
    test_coremail_config_processing()
    
    # 模拟连接测试
    test_connection_simulation(client)
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")
    print("=" * 60)
    
    if email_config and email_config.get('protocol', '').upper() == 'COREMAIL':
        print("✅ Coremail协议配置已正确识别")
    else:
        print("⚠️  Coremail协议配置可能存在问题")
    
    print("\n💡 提示:")
    print("1. 检查 resources/config/default_config.yaml 文件中的配置")
    print("2. 查看日志输出了解详细的加载过程")
    print("3. 如果仍有问题，请检查文件路径和权限")


if __name__ == "__main__":
    main()
