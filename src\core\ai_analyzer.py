#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析模块 - 集成硅基流动API
"""

import json
import requests
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from src.utils.logger import get_logger
from src.core.email_client import EmailMessage


class AnalysisType(Enum):
    """分析类型枚举"""
    SUMMARY = "summary"
    TODO = "todo"
    REPLY = "reply"


@dataclass
class AnalysisResult:
    """分析结果"""
    analysis_type: AnalysisType
    content: str
    confidence: float = 0.0
    tokens_used: int = 0
    processing_time: float = 0.0
    error: Optional[str] = None


class SiliconFlowClient:
    """硅基流动API客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger("SiliconFlowClient")
        
        self.api_key = config.get("api_key", "")
        self.base_url = config.get("base_url", "https://api.siliconflow.cn/v1")
        self.model = config.get("model", "Qwen/Qwen2.5-72B-Instruct")
        self.max_tokens = config.get("max_tokens", 2000)
        self.temperature = config.get("temperature", 0.7)
        
        # 请求头
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 验证配置
        if not self.api_key:
            self.logger.warning("硅基流动API密钥未配置")
    
    def _make_request(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """
        发送API请求
        
        Args:
            messages: 消息列表
            **kwargs: 其他参数
        
        Returns:
            Dict: API响应
        """
        try:
            # 构建请求数据
            data = {
                "model": self.model,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                "temperature": kwargs.get("temperature", self.temperature),
                "stream": False
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API请求失败: {e}")
            raise
        except Exception as e:
            self.logger.error(f"处理API响应失败: {e}")
            raise
    
    def analyze_text(self, text: str, prompt: str, **kwargs) -> str:
        """
        分析文本
        
        Args:
            text: 要分析的文本
            prompt: 分析提示词
            **kwargs: 其他参数
        
        Returns:
            str: 分析结果
        """
        try:
            # 构建消息
            messages = [
                {"role": "system", "content": "你是一个专业的邮件分析助手，请根据用户的要求对邮件内容进行分析。"},
                {"role": "user", "content": prompt.format(email_content=text)}
            ]
            
            # 发送请求
            response = self._make_request(messages, **kwargs)
            
            # 提取结果
            if "choices" in response and len(response["choices"]) > 0:
                return response["choices"][0]["message"]["content"].strip()
            else:
                raise Exception("API响应格式错误")
                
        except Exception as e:
            self.logger.error(f"文本分析失败: {e}")
            raise


class EmailAnalyzer:
    """邮件分析器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = get_logger("EmailAnalyzer")
        
        # 获取硅基流动配置
        sf_config = config_manager.get_siliconflow_config()
        self.sf_client = SiliconFlowClient(sf_config)
        
        # 默认提示词模板
        self.default_prompts = {
            AnalysisType.SUMMARY: """请对以下邮件内容进行总结，提取关键信息：

邮件内容：
{email_content}

请提供简洁的总结，包括：
1. 主要内容概述
2. 重要信息点
3. 发件人意图

请用中文回复，保持简洁明了。""",
            
            AnalysisType.TODO: """请从以下邮件内容中提取待办事项：

邮件内容：
{email_content}

请列出所有需要采取行动的事项，格式如下：
- [ ] 具体待办事项1
- [ ] 具体待办事项2

如果没有明确的待办事项，请回复"无明确待办事项"。
请用中文回复。""",
            
            AnalysisType.REPLY: """请为以下邮件生成建议回复内容：

原邮件内容：
{email_content}

请生成专业、礼貌的回复建议，包括：
1. 回复的主要内容
2. 可选的回复选项
3. 语气建议

请用中文回复，保持专业和礼貌的语调。"""
        }
    
    def get_prompt_template(self, analysis_type: AnalysisType) -> str:
        """
        获取提示词模板
        
        Args:
            analysis_type: 分析类型
        
        Returns:
            str: 提示词模板
        """
        # 尝试从配置中获取自定义提示词
        prompts_config = self.config_manager.get("prompts", {})
        
        if analysis_type == AnalysisType.SUMMARY:
            return prompts_config.get("summary", self.default_prompts[analysis_type])
        elif analysis_type == AnalysisType.TODO:
            return prompts_config.get("todo", self.default_prompts[analysis_type])
        elif analysis_type == AnalysisType.REPLY:
            return prompts_config.get("reply", self.default_prompts[analysis_type])
        else:
            return self.default_prompts.get(analysis_type, "")
    
    def analyze_email(self, email_msg: EmailMessage, analysis_type: AnalysisType) -> AnalysisResult:
        """
        分析邮件
        
        Args:
            email_msg: 邮件消息
            analysis_type: 分析类型
        
        Returns:
            AnalysisResult: 分析结果
        """
        import time
        start_time = time.time()
        
        try:
            # 准备邮件内容
            email_content = self._prepare_email_content(email_msg)
            
            if not email_content.strip():
                return AnalysisResult(
                    analysis_type=analysis_type,
                    content="邮件内容为空，无法进行分析",
                    error="邮件内容为空"
                )
            
            # 获取提示词模板
            prompt_template = self.get_prompt_template(analysis_type)
            
            if not prompt_template:
                return AnalysisResult(
                    analysis_type=analysis_type,
                    content="未找到对应的提示词模板",
                    error="提示词模板缺失"
                )
            
            # 调用AI分析
            self.logger.info(f"开始{analysis_type.value}分析")
            result_content = self.sf_client.analyze_text(email_content, prompt_template)
            
            processing_time = time.time() - start_time
            
            return AnalysisResult(
                analysis_type=analysis_type,
                content=result_content,
                confidence=0.8,  # 默认置信度
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"{analysis_type.value}分析失败: {str(e)}"
            self.logger.error(error_msg)
            
            return AnalysisResult(
                analysis_type=analysis_type,
                content="分析失败，请检查网络连接和API配置",
                processing_time=processing_time,
                error=error_msg
            )
    
    def _prepare_email_content(self, email_msg: EmailMessage) -> str:
        """
        准备邮件内容用于分析
        
        Args:
            email_msg: 邮件消息
        
        Returns:
            str: 准备好的邮件内容
        """
        content_parts = []
        
        # 添加邮件基本信息
        content_parts.append(f"主题: {email_msg.subject}")
        content_parts.append(f"发件人: {email_msg.sender}")
        
        if email_msg.date:
            content_parts.append(f"日期: {email_msg.date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        content_parts.append("=" * 50)
        
        # 添加邮件正文
        if email_msg.content_text:
            content_parts.append("邮件正文:")
            content_parts.append(email_msg.content_text)
        elif email_msg.content_html:
            # 如果只有HTML内容，简单处理一下
            import re
            # 移除HTML标签
            text_content = re.sub(r'<[^>]+>', '', email_msg.content_html)
            # 移除多余的空白字符
            text_content = re.sub(r'\s+', ' ', text_content).strip()
            
            content_parts.append("邮件正文:")
            content_parts.append(text_content)
        
        # 添加附件信息
        if email_msg.attachments:
            content_parts.append("\n附件:")
            for attachment in email_msg.attachments:
                content_parts.append(f"- {attachment.get('filename', '未知文件')}")
        
        return "\n".join(content_parts)
    
    def batch_analyze_emails(self, email_messages: List[EmailMessage], 
                           analysis_types: List[AnalysisType]) -> Dict[str, List[AnalysisResult]]:
        """
        批量分析邮件
        
        Args:
            email_messages: 邮件消息列表
            analysis_types: 分析类型列表
        
        Returns:
            Dict: 分析结果，键为邮件UID，值为分析结果列表
        """
        results = {}
        
        for email_msg in email_messages:
            email_results = []
            
            for analysis_type in analysis_types:
                try:
                    result = self.analyze_email(email_msg, analysis_type)
                    email_results.append(result)
                except Exception as e:
                    self.logger.error(f"批量分析邮件 {email_msg.uid} 失败: {e}")
                    error_result = AnalysisResult(
                        analysis_type=analysis_type,
                        content="分析失败",
                        error=str(e)
                    )
                    email_results.append(error_result)
            
            results[email_msg.uid] = email_results
        
        return results
    
    def update_prompts(self, prompts: Dict[str, str]):
        """
        更新提示词模板
        
        Args:
            prompts: 提示词字典
        """
        try:
            self.config_manager.set("prompts", prompts)
            self.logger.info("提示词模板已更新")
        except Exception as e:
            self.logger.error(f"更新提示词模板失败: {e}")
            raise
