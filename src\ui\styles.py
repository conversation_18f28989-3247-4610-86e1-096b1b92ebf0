#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI样式定义模块
"""

# 主题颜色
COLORS = {
    "primary": "#1976d2",
    "primary_light": "#42a5f5",
    "primary_dark": "#1565c0",
    "secondary": "#424242",
    "success": "#4caf50",
    "warning": "#ff9800",
    "error": "#f44336",
    "info": "#2196f3",
    "background": "#f5f5f5",
    "surface": "#ffffff",
    "border": "#dddddd",
    "text_primary": "#333333",
    "text_secondary": "#666666",
    "text_disabled": "#999999"
}

# 悬浮球样式
FLOATING_BALL_STYLE = """
QPushButton {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #4A90E2, stop:1 #357ABD);
    border: 2px solid #2E5C8A;
    border-radius: 30px;
    color: white;
    font-weight: bold;
    font-size: 12px;
}
QPushButton:hover {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #5BA0F2, stop:1 #4A8ACD);
    border: 2px solid #3E6C9A;
}
QPushButton:pressed {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #3A80D2, stop:1 #2E6AAD);
}
"""

# 主窗口样式
MAIN_WINDOW_STYLE = """
QMainWindow {
    background-color: #f5f5f5;
}

QPushButton {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: white;
    font-weight: bold;
    color: #333;
}

QPushButton:hover {
    background-color: #e3f2fd;
    border-color: #1976d2;
    color: #1976d2;
}

QPushButton:pressed {
    background-color: #bbdefb;
}

QPushButton:disabled {
    background-color: #f5f5f5;
    color: #999;
    border-color: #ddd;
}

QLabel {
    color: #333;
}

QComboBox, QSpinBox {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: white;
}

QComboBox:hover, QSpinBox:hover {
    border-color: #1976d2;
}

QComboBox:focus, QSpinBox:focus {
    border-color: #1976d2;
    outline: none;
}

QProgressBar {
    border: 1px solid #ddd;
    border-radius: 3px;
    text-align: center;
    background-color: #f5f5f5;
}

QProgressBar::chunk {
    background-color: #1976d2;
    border-radius: 2px;
}

QStatusBar {
    background-color: white;
    border-top: 1px solid #ddd;
}

QSplitter::handle {
    background-color: #ddd;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}
"""

# 邮件列表样式
EMAIL_LIST_STYLE = """
QListWidget {
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: white;
    outline: none;
}

QListWidget::item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    color: #333;
}

QListWidget::item:last-child {
    border-bottom: none;
}

QListWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
    border-left: 3px solid #1976d2;
}

QListWidget::item:hover {
    background-color: #f5f5f5;
}

QListWidget::item:selected:hover {
    background-color: #e1f5fe;
}
"""

# 分析结果样式
ANALYSIS_RESULT_STYLE = """
QTextEdit {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    font-size: 14px;
    line-height: 1.6;
    background-color: white;
    color: #333;
}

QTextEdit:focus {
    border-color: #1976d2;
    outline: none;
}

QTabWidget::pane {
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: white;
}

QTabBar::tab {
    padding: 10px 20px;
    margin-right: 2px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background-color: #f5f5f5;
    color: #666;
    font-weight: bold;
}

QTabBar::tab:selected {
    background-color: white;
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
}

QTabBar::tab:hover:!selected {
    background-color: #e3f2fd;
    color: #1976d2;
}
"""

# 配置对话框样式
CONFIG_DIALOG_STYLE = """
QDialog {
    background-color: #f5f5f5;
}

QTabWidget::pane {
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: white;
    padding: 10px;
}

QTabBar::tab {
    padding: 10px 20px;
    margin-right: 2px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background-color: #f5f5f5;
    color: #666;
    font-weight: bold;
}

QTabBar::tab:selected {
    background-color: white;
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
}

QGroupBox {
    font-weight: bold;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-top: 10px;
    padding-top: 10px;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 10px 0 10px;
    color: #1976d2;
}

QLineEdit, QTextEdit {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: white;
    color: #333;
}

QLineEdit:focus, QTextEdit:focus {
    border-color: #1976d2;
    outline: none;
}

QLineEdit:disabled, QTextEdit:disabled {
    background-color: #f5f5f5;
    color: #999;
}

QComboBox {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: white;
    color: #333;
}

QComboBox:hover {
    border-color: #1976d2;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #666;
    margin-right: 5px;
}

QSpinBox, QDoubleSpinBox {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: white;
    color: #333;
}

QSpinBox:hover, QDoubleSpinBox:hover {
    border-color: #1976d2;
}

QCheckBox {
    color: #333;
    font-weight: normal;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background-color: #1976d2;
    border-color: #1976d2;
}

QCheckBox::indicator:checked:hover {
    background-color: #1565c0;
}

QSlider::groove:horizontal {
    border: 1px solid #ddd;
    height: 6px;
    background-color: #f5f5f5;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #1976d2;
    border: 1px solid #1565c0;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #1565c0;
}

QSlider::sub-page:horizontal {
    background-color: #1976d2;
    border-radius: 3px;
}
"""

# 获取完整样式
def get_app_style():
    """获取应用程序完整样式"""
    return f"""
    {MAIN_WINDOW_STYLE}
    {EMAIL_LIST_STYLE}
    {ANALYSIS_RESULT_STYLE}
    {CONFIG_DIALOG_STYLE}
    """
