# CoreMail AI 邮件智能分析桌面应用

一个集成硅基流动大模型API的智能邮件分析桌面应用程序，支持Coremail邮件服务器连接，提供邮件内容总结、待办事项提取和建议回复生成功能。

## 功能特性

### 核心功能
- 🔗 连接Coremail邮件服务器，获取用户邮件数据
- 🤖 集成硅基流动(SiliconFlow)大模型API进行智能分析
- 📝 邮件内容总结
- ✅ 提取待办事项
- 💬 生成建议回复内容

### 用户配置功能
- ⚙️ 邮箱账户配置界面
- 🎯 AI模型配置选择
- 📋 自定义提示词设置

### 用户界面特性
- 🎈 桌面悬浮球UI设计
- 🖱️ 点击展开完整功能界面
- 📱 现代化美观界面设计
- 🔄 支持拖拽移动悬浮球位置

### 技术特性
- 🐍 Python开发
- 📧 IMAP/POP3邮件协议支持
- 🌐 HTTP API调用硅基流动服务
- 🖥️ PyQt6桌面GUI框架
- 💾 配置信息持久化存储
- 📊 批量处理多封邮件

## 项目结构

```
coremailAi/
├── src/                    # 源代码目录
│   ├── core/              # 核心功能模块
│   │   ├── __init__.py
│   │   ├── email_client.py    # 邮件客户端
│   │   ├── ai_analyzer.py     # AI分析模块
│   │   └── config_manager.py  # 配置管理
│   ├── ui/                # 用户界面模块
│   │   ├── __init__.py
│   │   ├── floating_ball.py   # 悬浮球界面
│   │   ├── main_window.py     # 主窗口界面
│   │   ├── config_dialog.py   # 配置对话框
│   │   └── styles.py          # 样式定义
│   ├── utils/             # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py          # 日志工具
│   │   └── helpers.py         # 辅助函数
│   └── main.py            # 主程序入口
├── resources/             # 资源文件
│   ├── icons/            # 图标文件
│   ├── styles/           # 样式文件
│   └── config/           # 配置模板
├── tests/                # 测试文件
├── requirements.txt      # 依赖包列表
├── setup.py             # 安装脚本
└── README.md            # 项目说明
```

## 安装与运行

### 环境要求
- Python 3.8+
- PyQt6
- 其他依赖见 requirements.txt

### 安装步骤
1. 克隆项目
```bash
git clone <repository-url>
cd coremailAi
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 运行应用
```bash
python src/main.py
```

## 配置说明

### 邮箱配置
- 服务器地址：Coremail服务器地址
- 用户名：邮箱用户名
- 密码：邮箱密码
- 协议：IMAP/POP3

### AI模型配置
- API密钥：硅基流动API密钥
- 模型选择：可选择不同的硅基流动模型
- 自定义提示词：个性化分析提示词

## 开发说明

本项目采用模块化设计，各模块职责清晰：
- `core/` 包含核心业务逻辑
- `ui/` 包含用户界面相关代码
- `utils/` 包含通用工具函数

## 许可证

MIT License
