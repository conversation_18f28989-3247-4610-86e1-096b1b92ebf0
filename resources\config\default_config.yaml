# 默认配置文件
app:
  name: "CoreMail AI"
  version: "1.0.0"
  debug: false

# 邮件服务器配置
email:
  server: "https://mails.dfmc.com.cn/coremail"
  port: 993
  protocol: "Coremail"  # IMAP 或 POP3
  username: ""
  password: ""
  ssl: true
  timeout: 30

# 硅基流动API配置
siliconflow:
  api_key: ""
  base_url: ""
  model: "Qwen/Qwen2.5-72B-Instruct"
  max_tokens: 2000
  temperature: 0.7

# 提示词模板
prompts:
  summary: |
    请对以下邮件内容进行总结，提取关键信息：
    
    邮件内容：
    {email_content}
    
    请提供简洁的总结，包括：
    1. 主要内容概述
    2. 重要信息点
    3. 发件人意图
  
  todo: |
    请从以下邮件内容中提取待办事项：
    
    邮件内容：
    {email_content}
    
    请列出所有需要采取行动的事项，格式如下：
    - [ ] 具体待办事项1
    - [ ] 具体待办事项2
  
  reply: |
    请为以下邮件生成建议回复内容：
    
    原邮件内容：
    {email_content}
    
    请生成专业、礼貌的回复建议，包括：
    1. 回复的主要内容
    2. 可选的回复选项
    3. 语气建议

# UI配置
ui:
  floating_ball:
    size: 60
    opacity: 0.8
    position:
      x: 100
      y: 100
  
  main_window:
    width: 1200
    height: 800
    min_width: 800
    min_height: 600

# 日志配置
logging:
  level: "INFO"
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5
