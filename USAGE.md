# CoreMail AI 使用说明

## 快速开始

### 1. 环境要求
- Python 3.8 或更高版本
- Windows/macOS/Linux 操作系统
- 网络连接（用于访问硅基流动API）

### 2. 安装与运行

#### 方法一：使用启动脚本（推荐）
```bash
python run.py
```
启动脚本会自动检查环境、安装依赖并启动应用程序。

#### 方法二：手动安装
```bash
# 安装依赖
pip install -r requirements.txt

# 运行应用
python src/main.py
```

### 3. 首次配置

启动应用后，您会看到一个悬浮球图标。右键点击悬浮球，选择"设置"进行配置：

#### 邮箱配置
1. **服务器地址**: 输入您的Coremail服务器地址（如：imap.coremail.cn）
2. **端口**: 通常IMAP使用993端口，POP3使用995端口
3. **协议**: 选择IMAP或POP3（推荐IMAP）
4. **用户名**: 您的邮箱地址
5. **密码**: 您的邮箱密码
6. **SSL/TLS**: 建议启用以确保安全

#### AI配置
1. **API密钥**: 输入您的硅基流动API密钥
   - 访问 [硅基流动官网](https://www.siliconflow.com/) 注册账号
   - 在控制台创建API密钥
2. **模型选择**: 选择合适的大语言模型
   - 推荐：Qwen/Qwen2.5-72B-Instruct（效果好）
   - 经济：Qwen/Qwen2.5-7B-Instruct（速度快）

#### 提示词配置（可选）
您可以自定义AI分析的提示词模板，以获得更符合需求的分析结果。

## 主要功能

### 1. 邮件列表查看
- 点击悬浮球展开主界面
- 点击"刷新邮件"获取最新邮件
- 支持选择不同文件夹（收件箱、已发送等）
- 可设置获取邮件数量限制

### 2. 智能分析
#### 单封邮件分析
1. 在邮件列表中选择一封邮件
2. 点击"分析选中"按钮
3. 查看分析结果：
   - **邮件总结**: 提取邮件关键信息
   - **待办事项**: 识别需要处理的任务
   - **回复建议**: 生成专业的回复内容

#### 批量分析
1. 点击"批量分析"按钮
2. 确认要分析的邮件数量
3. 等待分析完成
4. 查看批量分析结果摘要

### 3. 自动分析
启用"自动分析"选项后，选择邮件时会自动开始分析，无需手动点击分析按钮。

## 界面说明

### 悬浮球
- **左键点击**: 展开主界面
- **右键点击**: 显示菜单
  - 显示/隐藏悬浮球
  - 设置
  - 退出
- **拖拽**: 移动悬浮球位置

### 主界面
- **左侧**: 邮件列表
- **右侧**: AI分析结果
- **顶部工具栏**: 
  - 刷新邮件
  - 文件夹选择
  - 邮件数量设置
  - 自动分析开关
  - 分析按钮

### 系统托盘
应用会在系统托盘显示图标，关闭主窗口不会退出程序，而是最小化到托盘。

## 常见问题

### Q: 无法连接邮件服务器
**A**: 请检查：
1. 服务器地址是否正确
2. 端口号是否正确
3. 用户名密码是否正确
4. 网络连接是否正常
5. 是否启用了SSL/TLS

### Q: AI分析失败
**A**: 请检查：
1. 硅基流动API密钥是否正确
2. 网络连接是否正常
3. API配额是否充足
4. 邮件内容是否为空

### Q: 程序启动失败
**A**: 请检查：
1. Python版本是否为3.8+
2. 依赖包是否完整安装
3. 运行 `python run.py` 进行自动检查

### Q: 悬浮球不显示
**A**: 请尝试：
1. 检查系统托盘是否有应用图标
2. 双击托盘图标显示悬浮球
3. 重启应用程序

## 高级功能

### 自定义提示词
在设置中可以自定义AI分析的提示词模板：
- 使用 `{email_content}` 作为邮件内容占位符
- 可以针对不同分析类型设置不同的提示词
- 支持中英文提示词

### 配置文件
应用配置保存在用户目录下的 `.coremailai` 文件夹中：
- `config.yaml`: 主配置文件
- `key.key`: 加密密钥文件

### 日志文件
应用日志保存在 `logs/app.log` 文件中，可用于问题排查。

## 安全说明

1. **密码加密**: 邮箱密码使用AES加密存储
2. **API密钥**: 硅基流动API密钥明文存储在配置文件中，请妥善保管
3. **网络传输**: 邮件和API通信均使用SSL/TLS加密
4. **本地存储**: 邮件内容不会永久存储在本地

## 技术支持

如遇到问题，请：
1. 查看日志文件 `logs/app.log`
2. 运行测试脚本 `python tests/test_basic.py`
3. 检查网络连接和配置信息
4. 联系技术支持

## 更新日志

### v1.0.0
- 初始版本发布
- 支持Coremail邮件服务器连接
- 集成硅基流动AI分析
- 桌面悬浮球界面
- 批量邮件处理
- 配置管理和加密存储
