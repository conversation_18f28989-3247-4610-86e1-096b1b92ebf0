#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoreMail AI 安装脚本
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    with open(requirements_file, "r", encoding="utf-8") as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="coremailai",
    version="1.0.0",
    author="CoreMail AI Team",
    author_email="<EMAIL>",
    description="智能邮件分析桌面应用程序",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/coremailai/coremailai",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Communications :: Email",
        "Topic :: Office/Business",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-qt>=4.2.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=0.991",
        ],
    },
    entry_points={
        "console_scripts": [
            "coremailai=src.main:main",
        ],
        "gui_scripts": [
            "coremailai-gui=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src": [
            "resources/config/*.yaml",
            "resources/icons/*.png",
            "resources/icons/*.ico",
            "resources/styles/*.css",
        ],
    },
    zip_safe=False,
    keywords="email ai analysis desktop coremail siliconflow",
    project_urls={
        "Bug Reports": "https://github.com/coremailai/coremailai/issues",
        "Source": "https://github.com/coremailai/coremailai",
        "Documentation": "https://github.com/coremailai/coremailai/wiki",
    },
)
